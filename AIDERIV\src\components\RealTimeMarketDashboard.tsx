import React, { useState, useEffect } from 'react';
import { 
  TrendingUp, TrendingDown, Activity, AlertCircle, 
  Bell, BellOff, Target, BarChart3, Clock, Globe 
} from 'lucide-react';
import { useTradingStore } from '../store/tradingStore';
import { useTrading } from '../hooks/useTrading';
import { MarketSymbol } from '../types/trading';

interface MarketTile {
  symbol: MarketSymbol;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  trend: 'up' | 'down' | 'neutral';
  volume: number;
  isOpen: boolean;
}

interface PriceAlert {
  id: string;
  symbol: string;
  type: 'price_above' | 'price_below' | 'change_percent' | 'volatility';
  threshold: number;
  message: string;
  active: boolean;
}

export const RealTimeMarketDashboard: React.FC = () => {
  const { marketData, currentMarket } = useTradingStore();
  const { 
    getMarketInfo, 
    getMarketCondition, 
    addPriceAlert, 
    removePriceAlert, 
    getPriceAlerts,
    changeMarket 
  } = useTrading();

  const [selectedMarkets, setSelectedMarkets] = useState<MarketSymbol[]>([
    'R_100', 'R_50', 'R_25', 'R_10', 'BOOM_1000', 'CRASH_1000'
  ]);
  const [marketTiles, setMarketTiles] = useState<MarketTile[]>([]);
  const [priceAlerts, setPriceAlerts] = useState<PriceAlert[]>([]);
  const [showAlertModal, setShowAlertModal] = useState(false);
  const [newAlert, setNewAlert] = useState({
    symbol: currentMarket,
    type: 'price_above' as const,
    threshold: 0,
    message: '',
  });

  useEffect(() => {
    // Update market tiles with real data
    const tiles: MarketTile[] = selectedMarkets.map(symbol => {
      const data = marketData[symbol];
      const info = getMarketInfo(symbol);
      
      if (!data) {
        return {
          symbol,
          name: info?.display_name || symbol,
          price: 0,
          change: 0,
          changePercent: 0,
          trend: 'neutral' as const,
          volume: 0,
          isOpen: info?.exchange_is_open === 1,
        };
      }

      const trend = data.change > 0 ? 'up' : data.change < 0 ? 'down' : 'neutral';
      
      return {
        symbol,
        name: info?.display_name || symbol,
        price: data.lastPrice,
        change: data.change,
        changePercent: data.changePercent,
        trend,
        volume: data.volume || 0,
        isOpen: info?.exchange_is_open === 1,
      };
    });

    setMarketTiles(tiles);
  }, [marketData, selectedMarkets, getMarketInfo]);

  useEffect(() => {
    // Load price alerts
    const alerts = getPriceAlerts().map(alert => ({
      id: alert.id,
      symbol: alert.symbol,
      type: alert.type,
      threshold: alert.threshold,
      message: alert.message,
      active: !alert.triggered,
    }));
    setPriceAlerts(alerts);
  }, [getPriceAlerts]);

  const handleAddAlert = () => {
    if (!newAlert.message || newAlert.threshold <= 0) {
      return;
    }

    const alertId = addPriceAlert({
      symbol: newAlert.symbol,
      type: newAlert.type,
      threshold: newAlert.threshold,
      message: newAlert.message,
    });

    setPriceAlerts(prev => [...prev, {
      id: alertId,
      ...newAlert,
      active: true,
    }]);

    setNewAlert({
      symbol: currentMarket,
      type: 'price_above',
      threshold: 0,
      message: '',
    });
    setShowAlertModal(false);
  };

  const handleRemoveAlert = (id: string) => {
    removePriceAlert(id);
    setPriceAlerts(prev => prev.filter(alert => alert.id !== id));
  };

  const formatPrice = (price: number, symbol: string) => {
    const decimals = symbol.includes('BOOM') || symbol.includes('CRASH') ? 3 : 5;
    return price.toFixed(decimals);
  };

  const formatChange = (change: number, changePercent: number) => {
    const sign = change >= 0 ? '+' : '';
    return `${sign}${change.toFixed(5)} (${sign}${changePercent.toFixed(2)}%)`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white">Real-Time Market Data</h2>
        <div className="flex space-x-2">
          <button
            onClick={() => setShowAlertModal(true)}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Bell className="w-4 h-4 mr-2" />
            Add Alert
          </button>
        </div>
      </div>

      {/* Market Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {marketTiles.map((tile) => (
          <div
            key={tile.symbol}
            onClick={() => changeMarket(tile.symbol)}
            className={`bg-gray-800 rounded-lg p-4 border cursor-pointer transition-all hover:bg-gray-750 ${
              currentMarket === tile.symbol 
                ? 'border-blue-500 ring-2 ring-blue-500 ring-opacity-50' 
                : 'border-gray-700 hover:border-gray-600'
            }`}
          >
            <div className="flex items-center justify-between mb-2">
              <div>
                <h3 className="font-semibold text-white">{tile.symbol}</h3>
                <p className="text-sm text-gray-400">{tile.name}</p>
              </div>
              <div className="flex items-center space-x-2">
                {tile.isOpen ? (
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                ) : (
                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                )}
                {tile.trend === 'up' && <TrendingUp className="w-4 h-4 text-green-500" />}
                {tile.trend === 'down' && <TrendingDown className="w-4 h-4 text-red-500" />}
                {tile.trend === 'neutral' && <Activity className="w-4 h-4 text-gray-500" />}
              </div>
            </div>

            <div className="space-y-1">
              <div className="text-2xl font-bold text-white">
                {formatPrice(tile.price, tile.symbol)}
              </div>
              <div className={`text-sm ${
                tile.change >= 0 ? 'text-green-500' : 'text-red-500'
              }`}>
                {formatChange(tile.change, tile.changePercent)}
              </div>
              {tile.volume > 0 && (
                <div className="text-xs text-gray-500">
                  Vol: {tile.volume.toLocaleString()}
                </div>
              )}
            </div>

            <div className="mt-3 flex items-center justify-between text-xs text-gray-500">
              <span>{tile.isOpen ? 'Market Open' : 'Market Closed'}</span>
              <span className="flex items-center">
                <Clock className="w-3 h-3 mr-1" />
                Live
              </span>
            </div>
          </div>
        ))}
      </div>

      {/* Active Price Alerts */}
      {priceAlerts.length > 0 && (
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <AlertCircle className="w-5 h-5 mr-2 text-yellow-500" />
            Active Price Alerts ({priceAlerts.filter(a => a.active).length})
          </h3>
          <div className="space-y-2">
            {priceAlerts.filter(alert => alert.active).map((alert) => (
              <div
                key={alert.id}
                className="flex items-center justify-between p-3 bg-gray-700 rounded-lg"
              >
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-white">{alert.symbol}</span>
                    <span className="text-sm text-gray-400">
                      {alert.type.replace('_', ' ').toUpperCase()}
                    </span>
                    <span className="text-sm text-blue-400">{alert.threshold}</span>
                  </div>
                  <p className="text-sm text-gray-300 mt-1">{alert.message}</p>
                </div>
                <button
                  onClick={() => handleRemoveAlert(alert.id)}
                  className="text-red-400 hover:text-red-300 transition-colors"
                >
                  <BellOff className="w-4 h-4" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Add Alert Modal */}
      {showAlertModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md border border-gray-700">
            <h3 className="text-lg font-semibold text-white mb-4">Add Price Alert</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Market Symbol
                </label>
                <select
                  value={newAlert.symbol}
                  onChange={(e) => setNewAlert(prev => ({ ...prev, symbol: e.target.value as MarketSymbol }))}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {selectedMarkets.map(symbol => (
                    <option key={symbol} value={symbol}>{symbol}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Alert Type
                </label>
                <select
                  value={newAlert.type}
                  onChange={(e) => setNewAlert(prev => ({ ...prev, type: e.target.value as any }))}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="price_above">Price Above</option>
                  <option value="price_below">Price Below</option>
                  <option value="change_percent">Change Percent</option>
                  <option value="volatility">High Volatility</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Threshold
                </label>
                <input
                  type="number"
                  step="0.00001"
                  value={newAlert.threshold}
                  onChange={(e) => setNewAlert(prev => ({ ...prev, threshold: parseFloat(e.target.value) || 0 }))}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter threshold value"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Alert Message
                </label>
                <input
                  type="text"
                  value={newAlert.message}
                  onChange={(e) => setNewAlert(prev => ({ ...prev, message: e.target.value }))}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter alert message"
                />
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={handleAddAlert}
                className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Add Alert
              </button>
              <button
                onClick={() => setShowAlertModal(false)}
                className="flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
