import { describe, it, expect, beforeEach, vi } from 'vitest';
import { getRiskManager } from '../services/riskManager';
import { Trade, AccountInfo, TradingSettings, MarketData } from '../types/trading';

describe('RiskManager', () => {
  let riskManager: ReturnType<typeof getRiskManager>;
  let mockAccount: AccountInfo;
  let mockTrades: Trade[];
  let mockSettings: TradingSettings;
  let mockMarketData: MarketData;

  beforeEach(() => {
    riskManager = getRiskManager();
    
    mockAccount = {
      balance: 1000,
      currency: 'USD',
      loginid: 'test123',
      email: '<EMAIL>',
    };

    mockSettings = {
      stake: 10,
      duration: 5,
      targetProfit: 100,
      stopLoss: 50,
      prediction: 5,
      autoTrading: false,
      riskLevel: 'medium',
    };

    mockMarketData = {
      symbol: 'R_100',
      lastPrice: 1000.50,
      change: 0.25,
      changePercent: 0.025,
      digits: [1, 0, 0, 0, 5, 0],
      timestamp: Date.now(),
    };

    // Create mock trades with varying profits/losses
    mockTrades = [
      {
        id: '1',
        symbol: 'R_100',
        mode: 'ai',
        direction: 'up',
        stake: 10,
        duration: 5,
        timestamp: Date.now() - ********, // 1 day ago
        status: 'completed',
        profit: 8,
        entryPrice: 1000,
        exitPrice: 1008,
      },
      {
        id: '2',
        symbol: 'R_100',
        mode: 'ai',
        direction: 'down',
        stake: 10,
        duration: 5,
        timestamp: Date.now() - 82800000,
        status: 'completed',
        profit: -10,
        entryPrice: 1000,
        exitPrice: 990,
      },
      {
        id: '3',
        symbol: 'R_100',
        mode: 'over_under',
        direction: 'up',
        stake: 15,
        duration: 5,
        timestamp: Date.now() - 79200000,
        status: 'completed',
        profit: 12,
        entryPrice: 1000,
        exitPrice: 1012,
      },
      {
        id: '4',
        symbol: 'R_100',
        mode: 'ai',
        direction: 'down',
        stake: 10,
        duration: 5,
        timestamp: Date.now() - ********,
        status: 'completed',
        profit: -10,
        entryPrice: 1000,
        exitPrice: 990,
      },
      {
        id: '5',
        symbol: 'R_100',
        mode: 'ai',
        direction: 'up',
        stake: 10,
        duration: 5,
        timestamp: Date.now() - ********,
        status: 'completed',
        profit: 9,
        entryPrice: 1000,
        exitPrice: 1009,
      },
    ];

    riskManager.updateAccount(mockAccount);
    riskManager.updateTrades(mockTrades);
  });

  describe('calculateRiskMetrics', () => {
    it('should calculate correct win rate', () => {
      const metrics = riskManager.calculateRiskMetrics();
      
      // 3 wins out of 5 trades = 60%
      expect(metrics.winRate).toBeCloseTo(0.6, 2);
    });

    it('should calculate correct profit factor', () => {
      const metrics = riskManager.calculateRiskMetrics();
      
      // Total wins: 8 + 12 + 9 = 29
      // Total losses: 10 + 10 = 20
      // Profit factor: 29/20 = 1.45
      expect(metrics.profitFactor).toBeCloseTo(1.45, 2);
    });

    it('should calculate average win and loss correctly', () => {
      const metrics = riskManager.calculateRiskMetrics();
      
      // Average win: (8 + 12 + 9) / 3 = 9.67
      expect(metrics.averageWin).toBeCloseTo(9.67, 2);
      
      // Average loss: (10 + 10) / 2 = 10
      expect(metrics.averageLoss).toBeCloseTo(10, 2);
    });

    it('should handle empty trades array', () => {
      riskManager.updateTrades([]);
      const metrics = riskManager.calculateRiskMetrics();
      
      expect(metrics.winRate).toBe(0);
      expect(metrics.profitFactor).toBe(0);
      expect(metrics.averageWin).toBe(0);
      expect(metrics.averageLoss).toBe(0);
      expect(metrics.maxDrawdown).toBe(0);
    });

    it('should calculate maximum drawdown correctly', () => {
      // Create trades with specific sequence to test drawdown
      const drawdownTrades: Trade[] = [
        { ...mockTrades[0], profit: 10, timestamp: Date.now() - 5000 },
        { ...mockTrades[1], profit: -5, timestamp: Date.now() - 4000 },
        { ...mockTrades[2], profit: -8, timestamp: Date.now() - 3000 },
        { ...mockTrades[3], profit: -3, timestamp: Date.now() - 2000 },
        { ...mockTrades[4], profit: 15, timestamp: Date.now() - 1000 },
      ];
      
      riskManager.updateTrades(drawdownTrades);
      const metrics = riskManager.calculateRiskMetrics();
      
      expect(metrics.maxDrawdown).toBeGreaterThan(0);
    });
  });

  describe('calculatePositionSizing', () => {
    it('should recommend appropriate position size', () => {
      const positioning = riskManager.calculatePositionSizing(mockSettings, mockMarketData, 0.8);
      
      expect(positioning.recommendedStake).toBeGreaterThan(0);
      expect(positioning.maxStake).toBeGreaterThan(positioning.recommendedStake);
      expect(positioning.riskPercentage).toBeGreaterThan(0);
      expect(positioning.riskPercentage).toBeLessThan(0.1); // Should not risk more than 10%
    });

    it('should reduce stake after consecutive losses', () => {
      // Create trades with consecutive losses
      const consecutiveLossTrades: Trade[] = Array.from({ length: 4 }, (_, i) => ({
        ...mockTrades[0],
        id: `loss_${i}`,
        profit: -10,
        timestamp: Date.now() - (4 - i) * 1000,
      }));
      
      riskManager.updateTrades(consecutiveLossTrades);
      const positioning = riskManager.calculatePositionSizing(mockSettings, mockMarketData, 0.8);
      
      // Should recommend lower stake due to consecutive losses
      expect(positioning.recommendedStake).toBeLessThan(mockSettings.stake);
    });

    it('should respect maximum stake limits', () => {
      const positioning = riskManager.calculatePositionSizing(mockSettings, mockMarketData, 0.9);
      
      // Max stake should not exceed 5% of account balance
      expect(positioning.maxStake).toBeLessThanOrEqual(mockAccount.balance * 0.05);
    });

    it('should handle high confidence appropriately', () => {
      const highConfidencePositioning = riskManager.calculatePositionSizing(mockSettings, mockMarketData, 0.95);
      const lowConfidencePositioning = riskManager.calculatePositionSizing(mockSettings, mockMarketData, 0.6);
      
      expect(highConfidencePositioning.recommendedStake).toBeGreaterThanOrEqual(lowConfidencePositioning.recommendedStake);
    });
  });

  describe('shouldAllowTrade', () => {
    it('should allow trade under normal conditions', () => {
      const result = riskManager.shouldAllowTrade(mockSettings);
      
      expect(result.allowed).toBe(true);
      expect(result.reason).toBeUndefined();
    });

    it('should block trade after too many consecutive losses', () => {
      // Create 6 consecutive losses (exceeds default limit of 5)
      const consecutiveLossTrades: Trade[] = Array.from({ length: 6 }, (_, i) => ({
        ...mockTrades[0],
        id: `loss_${i}`,
        profit: -10,
        timestamp: Date.now() - (6 - i) * 1000,
      }));
      
      riskManager.updateTrades(consecutiveLossTrades);
      const result = riskManager.shouldAllowTrade(mockSettings);
      
      expect(result.allowed).toBe(false);
      expect(result.reason).toContain('consecutive losses');
    });

    it('should block trade if stake exceeds maximum', () => {
      const highStakeSettings = { ...mockSettings, stake: 100 }; // 10% of account
      const result = riskManager.shouldAllowTrade(highStakeSettings);
      
      expect(result.allowed).toBe(false);
      expect(result.reason).toContain('maximum allowed');
    });

    it('should block trade if daily loss limit exceeded', () => {
      // Create trades that exceed daily loss limit
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      const dailyLossTrades: Trade[] = Array.from({ length: 3 }, (_, i) => ({
        ...mockTrades[0],
        id: `daily_loss_${i}`,
        profit: -40, // Total: -120, which is 12% of 1000 balance
        timestamp: today.getTime() + i * 1000,
      }));
      
      riskManager.updateTrades(dailyLossTrades);
      const result = riskManager.shouldAllowTrade(mockSettings);
      
      expect(result.allowed).toBe(false);
      expect(result.reason).toContain('Daily loss limit');
    });
  });

  describe('getRiskAlerts', () => {
    it('should generate alerts for high risk conditions', () => {
      // Create high-risk scenario
      const highRiskTrades: Trade[] = Array.from({ length: 6 }, (_, i) => ({
        ...mockTrades[0],
        id: `risk_${i}`,
        profit: -15,
        timestamp: Date.now() - (6 - i) * 1000,
      }));
      
      riskManager.updateTrades(highRiskTrades);
      const alerts = riskManager.getRiskAlerts();
      
      expect(alerts.length).toBeGreaterThan(0);
      expect(alerts[0]).toHaveProperty('type');
      expect(alerts[0]).toHaveProperty('message');
      expect(alerts[0]).toHaveProperty('severity');
      expect(alerts[0]).toHaveProperty('timestamp');
    });

    it('should sort alerts by severity', () => {
      // Create multiple risk conditions
      const multiRiskTrades: Trade[] = [
        ...Array.from({ length: 6 }, (_, i) => ({
          ...mockTrades[0],
          id: `consecutive_${i}`,
          profit: -10,
          timestamp: Date.now() - (6 - i) * 1000,
        })),
        ...Array.from({ length: 5 }, (_, i) => ({
          ...mockTrades[0],
          id: `low_win_${i}`,
          profit: i % 4 === 0 ? 5 : -10, // 20% win rate
          timestamp: Date.now() - (11 - i) * 1000,
        })),
      ];
      
      riskManager.updateTrades(multiRiskTrades);
      const alerts = riskManager.getRiskAlerts();
      
      if (alerts.length > 1) {
        for (let i = 1; i < alerts.length; i++) {
          expect(alerts[i - 1].severity).toBeGreaterThanOrEqual(alerts[i].severity);
        }
      }
    });
  });

  describe('updateSettings', () => {
    it('should update risk settings correctly', () => {
      const newSettings = {
        maxConsecutiveLosses: 3,
        maxDailyLoss: 0.05,
        maxDrawdown: 0.15,
      };
      
      riskManager.updateSettings(newSettings);
      
      // Test that new settings are applied
      const consecutiveLossTrades: Trade[] = Array.from({ length: 4 }, (_, i) => ({
        ...mockTrades[0],
        id: `loss_${i}`,
        profit: -10,
        timestamp: Date.now() - (4 - i) * 1000,
      }));
      
      riskManager.updateTrades(consecutiveLossTrades);
      const result = riskManager.shouldAllowTrade(mockSettings);
      
      expect(result.allowed).toBe(false); // Should block at 3 losses instead of 5
    });

    it('should persist settings to localStorage', () => {
      const localStorageSpy = vi.spyOn(Storage.prototype, 'setItem');
      
      riskManager.updateSettings({ maxConsecutiveLosses: 7 });
      
      expect(localStorageSpy).toHaveBeenCalledWith(
        'riskManagerSettings',
        expect.stringContaining('maxConsecutiveLosses')
      );
    });
  });

  describe('Performance', () => {
    it('should handle large number of trades efficiently', () => {
      const largeTrades: Trade[] = Array.from({ length: 1000 }, (_, i) => ({
        ...mockTrades[0],
        id: `large_${i}`,
        profit: Math.random() * 20 - 10,
        timestamp: Date.now() - i * 1000,
      }));
      
      const startTime = performance.now();
      riskManager.updateTrades(largeTrades);
      riskManager.calculateRiskMetrics();
      const endTime = performance.now();
      
      expect(endTime - startTime).toBeLessThan(100); // Should complete within 100ms
    });
  });
});
