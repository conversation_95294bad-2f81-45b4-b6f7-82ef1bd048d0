import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import TradingPanel from '../components/TradingPanel';
import { useTradingStore } from '../store/tradingStore';

// Mock the trading store
vi.mock('../store/tradingStore', () => ({
  useTradingStore: vi.fn(),
}));

// Mock the trading hook
vi.mock('../hooks/useTrading', () => ({
  useTrading: () => ({
    placeTrade: vi.fn(),
    isConnected: true,
  }),
}));

// Mock toast notifications
vi.mock('react-hot-toast', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    loading: vi.fn(),
  },
}));

describe('TradingPanel', () => {
  const mockStore = {
    currentMode: 'ai' as const,
    settings: {
      ai: {
        stake: 10,
        duration: 5,
        targetProfit: 100,
        stopLoss: 50,
        prediction: 5,
        autoTrading: false,
        riskLevel: 'medium' as const,
      },
      over_under: {
        stake: 10,
        duration: 5,
        targetProfit: 100,
        stopLoss: 50,
        prediction: 5,
        autoTrading: false,
        riskLevel: 'medium' as const,
      },
      diff_match: {
        stake: 10,
        duration: 5,
        targetProfit: 100,
        stopLoss: 50,
        prediction: 5,
        autoTrading: false,
        riskLevel: 'medium' as const,
      },
      rise_fall: {
        stake: 10,
        duration: 5,
        targetProfit: 100,
        stopLoss: 50,
        prediction: 5,
        autoTrading: false,
        riskLevel: 'medium' as const,
      },
    },
    currentMarket: 'R_100' as const,
    account: {
      balance: 1000,
      currency: 'USD',
      loginid: 'test123',
      email: '<EMAIL>',
    },
    setCurrentMode: vi.fn(),
    updateSettings: vi.fn(),
  };

  beforeEach(() => {
    vi.mocked(useTradingStore).mockReturnValue(mockStore as any);
  });

  it('renders trading panel with all modes', () => {
    render(<TradingPanel />);
    
    expect(screen.getByText('AI ✨')).toBeInTheDocument();
    expect(screen.getByText('Over | Under')).toBeInTheDocument();
    expect(screen.getByText('Diff | Match')).toBeInTheDocument();
    expect(screen.getByText('Rise | Fall')).toBeInTheDocument();
  });

  it('displays current mode as active', () => {
    render(<TradingPanel />);
    
    const aiButton = screen.getByText('AI ✨').closest('button');
    expect(aiButton).toHaveClass('bg-blue-600');
  });

  it('switches modes when clicking mode buttons', async () => {
    const user = userEvent.setup();
    render(<TradingPanel />);
    
    const overUnderButton = screen.getByText('Over | Under').closest('button');
    await user.click(overUnderButton!);
    
    expect(mockStore.setCurrentMode).toHaveBeenCalledWith('over_under');
  });

  it('displays stake input with current value', () => {
    render(<TradingPanel />);
    
    const stakeInput = screen.getByDisplayValue('10');
    expect(stakeInput).toBeInTheDocument();
  });

  it('updates stake when input changes', async () => {
    const user = userEvent.setup();
    render(<TradingPanel />);
    
    const stakeInput = screen.getByDisplayValue('10');
    await user.clear(stakeInput);
    await user.type(stakeInput, '25');
    
    expect(mockStore.updateSettings).toHaveBeenCalledWith('ai', { stake: 25 });
  });

  it('displays duration selector', () => {
    render(<TradingPanel />);
    
    expect(screen.getByText('Duration')).toBeInTheDocument();
    expect(screen.getByDisplayValue('5')).toBeInTheDocument();
  });

  it('shows prediction input for over_under mode', () => {
    mockStore.currentMode = 'over_under';
    render(<TradingPanel />);
    
    expect(screen.getByText('Prediction')).toBeInTheDocument();
  });

  it('shows target profit and stop loss for AI mode', () => {
    render(<TradingPanel />);
    
    expect(screen.getByText('Target Profit')).toBeInTheDocument();
    expect(screen.getByText('Stop Loss')).toBeInTheDocument();
  });

  it('displays risk level selector for AI mode', () => {
    render(<TradingPanel />);
    
    expect(screen.getByText('Risk Level')).toBeInTheDocument();
    expect(screen.getByText('Medium')).toBeInTheDocument();
  });

  it('shows auto trading toggle for AI mode', () => {
    render(<TradingPanel />);
    
    expect(screen.getByText('Auto Trading')).toBeInTheDocument();
  });

  it('displays account balance', () => {
    render(<TradingPanel />);
    
    expect(screen.getByText('Balance: $1,000.00')).toBeInTheDocument();
  });

  it('shows UP and DOWN buttons for rise_fall mode', () => {
    mockStore.currentMode = 'rise_fall';
    render(<TradingPanel />);
    
    expect(screen.getByText('UP')).toBeInTheDocument();
    expect(screen.getByText('DOWN')).toBeInTheDocument();
  });

  it('validates stake input', async () => {
    const user = userEvent.setup();
    render(<TradingPanel />);
    
    const stakeInput = screen.getByDisplayValue('10');
    await user.clear(stakeInput);
    await user.type(stakeInput, '0');
    
    // Should not allow zero stake
    expect(stakeInput).toHaveValue(1); // Minimum stake
  });

  it('validates stake against account balance', async () => {
    const user = userEvent.setup();
    render(<TradingPanel />);
    
    const stakeInput = screen.getByDisplayValue('10');
    await user.clear(stakeInput);
    await user.type(stakeInput, '2000'); // More than balance
    
    // Should cap at reasonable percentage of balance
    expect(mockStore.updateSettings).toHaveBeenCalledWith('ai', 
      expect.objectContaining({ stake: expect.any(Number) })
    );
  });

  it('disables trading when not connected', () => {
    vi.mocked(require('../hooks/useTrading').useTrading).mockReturnValue({
      placeTrade: vi.fn(),
      isConnected: false,
    });
    
    render(<TradingPanel />);
    
    const tradeButtons = screen.getAllByRole('button').filter(button => 
      button.textContent?.includes('UP') || 
      button.textContent?.includes('DOWN') ||
      button.textContent?.includes('TRADE')
    );
    
    tradeButtons.forEach(button => {
      expect(button).toBeDisabled();
    });
  });

  it('shows loading state during trade execution', async () => {
    const mockPlaceTrade = vi.fn().mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));
    vi.mocked(require('../hooks/useTrading').useTrading).mockReturnValue({
      placeTrade: mockPlaceTrade,
      isConnected: true,
    });
    
    const user = userEvent.setup();
    mockStore.currentMode = 'rise_fall';
    render(<TradingPanel />);
    
    const upButton = screen.getByText('UP');
    await user.click(upButton);
    
    expect(screen.getByText('Placing Trade...')).toBeInTheDocument();
  });

  it('handles trade placement errors gracefully', async () => {
    const mockPlaceTrade = vi.fn().mockRejectedValue(new Error('Trade failed'));
    vi.mocked(require('../hooks/useTrading').useTrading).mockReturnValue({
      placeTrade: mockPlaceTrade,
      isConnected: true,
    });
    
    const user = userEvent.setup();
    mockStore.currentMode = 'rise_fall';
    render(<TradingPanel />);
    
    const upButton = screen.getByText('UP');
    await user.click(upButton);
    
    await waitFor(() => {
      expect(require('react-hot-toast').toast.error).toHaveBeenCalled();
    });
  });

  it('updates settings when risk level changes', async () => {
    const user = userEvent.setup();
    render(<TradingPanel />);
    
    // Find and click the risk level selector
    const riskLevelButton = screen.getByText('Medium');
    await user.click(riskLevelButton);
    
    // Select high risk
    const highRiskOption = screen.getByText('High');
    await user.click(highRiskOption);
    
    expect(mockStore.updateSettings).toHaveBeenCalledWith('ai', { riskLevel: 'high' });
  });

  it('toggles auto trading correctly', async () => {
    const user = userEvent.setup();
    render(<TradingPanel />);
    
    const autoTradingToggle = screen.getByRole('switch');
    await user.click(autoTradingToggle);
    
    expect(mockStore.updateSettings).toHaveBeenCalledWith('ai', { autoTrading: true });
  });

  it('shows appropriate controls for each mode', () => {
    // Test AI mode
    mockStore.currentMode = 'ai';
    const { rerender } = render(<TradingPanel />);
    expect(screen.getByText('Target Profit')).toBeInTheDocument();
    expect(screen.getByText('Auto Trading')).toBeInTheDocument();
    
    // Test Over/Under mode
    mockStore.currentMode = 'over_under';
    rerender(<TradingPanel />);
    expect(screen.getByText('Prediction')).toBeInTheDocument();
    
    // Test Diff/Match mode
    mockStore.currentMode = 'diff_match';
    rerender(<TradingPanel />);
    expect(screen.getByText('Prediction')).toBeInTheDocument();
    
    // Test Rise/Fall mode
    mockStore.currentMode = 'rise_fall';
    rerender(<TradingPanel />);
    expect(screen.getByText('UP')).toBeInTheDocument();
    expect(screen.getByText('DOWN')).toBeInTheDocument();
  });

  it('formats currency values correctly', () => {
    render(<TradingPanel />);
    
    expect(screen.getByText('Balance: $1,000.00')).toBeInTheDocument();
  });

  it('respects minimum and maximum stake limits', async () => {
    const user = userEvent.setup();
    render(<TradingPanel />);
    
    const stakeInput = screen.getByDisplayValue('10');
    
    // Test minimum stake
    await user.clear(stakeInput);
    await user.type(stakeInput, '0.5');
    expect(mockStore.updateSettings).toHaveBeenCalledWith('ai', { stake: 1 }); // Minimum 1
    
    // Test maximum stake (should be reasonable percentage of balance)
    await user.clear(stakeInput);
    await user.type(stakeInput, '500'); // 50% of balance
    expect(mockStore.updateSettings).toHaveBeenCalledWith('ai', 
      expect.objectContaining({ stake: expect.any(Number) })
    );
  });
});
