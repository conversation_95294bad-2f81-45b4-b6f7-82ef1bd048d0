import { Trade, TradingSettings, AccountInfo, MarketData } from '../types/trading';

export interface RiskMetrics {
  maxDrawdown: number;
  sharpeRatio: number;
  winRate: number;
  profitFactor: number;
  averageWin: number;
  averageLoss: number;
  consecutiveLosses: number;
  riskRewardRatio: number;
  volatility: number;
  var95: number; // Value at Risk 95%
}

export interface PositionSizing {
  recommendedStake: number;
  maxStake: number;
  riskPercentage: number;
  kellyPercentage: number;
}

export interface RiskAlert {
  type: 'warning' | 'danger' | 'info';
  message: string;
  severity: number; // 1-10
  timestamp: number;
}

export class RiskManager {
  private trades: Trade[] = [];
  private account: AccountInfo | null = null;
  private riskAlerts: RiskAlert[] = [];
  private maxConsecutiveLosses = 5;
  private maxDailyLoss = 0.1; // 10% of account
  private maxDrawdown = 0.2; // 20% of account

  constructor() {
    this.loadSettings();
  }

  private loadSettings(): void {
    try {
      const settings = localStorage.getItem('riskManagerSettings');
      if (settings) {
        const parsed = JSON.parse(settings);
        this.maxConsecutiveLosses = parsed.maxConsecutiveLosses || 5;
        this.maxDailyLoss = parsed.maxDailyLoss || 0.1;
        this.maxDrawdown = parsed.maxDrawdown || 0.2;
      }
    } catch (error) {
      console.error('Failed to load risk manager settings:', error);
    }
  }

  updateTrades(trades: Trade[]): void {
    this.trades = trades;
    this.checkRiskAlerts();
  }

  updateAccount(account: AccountInfo): void {
    this.account = account;
  }

  calculateRiskMetrics(): RiskMetrics {
    const completedTrades = this.trades.filter(t => t.status === 'completed');
    
    if (completedTrades.length === 0) {
      return {
        maxDrawdown: 0,
        sharpeRatio: 0,
        winRate: 0,
        profitFactor: 0,
        averageWin: 0,
        averageLoss: 0,
        consecutiveLosses: 0,
        riskRewardRatio: 0,
        volatility: 0,
        var95: 0,
      };
    }

    const profits = completedTrades.map(t => t.profit || 0);
    const wins = profits.filter(p => p > 0);
    const losses = profits.filter(p => p < 0);

    const winRate = wins.length / completedTrades.length;
    const averageWin = wins.length > 0 ? wins.reduce((a, b) => a + b, 0) / wins.length : 0;
    const averageLoss = losses.length > 0 ? Math.abs(losses.reduce((a, b) => a + b, 0) / losses.length) : 0;
    const profitFactor = averageLoss > 0 ? (averageWin * wins.length) / (averageLoss * losses.length) : 0;

    // Calculate drawdown
    let peak = 0;
    let maxDrawdown = 0;
    let runningTotal = 0;

    for (const profit of profits) {
      runningTotal += profit;
      if (runningTotal > peak) {
        peak = runningTotal;
      }
      const drawdown = (peak - runningTotal) / Math.max(peak, 1);
      maxDrawdown = Math.max(maxDrawdown, drawdown);
    }

    // Calculate consecutive losses
    let consecutiveLosses = 0;
    let maxConsecutiveLosses = 0;
    for (const profit of profits.reverse()) {
      if (profit < 0) {
        consecutiveLosses++;
        maxConsecutiveLosses = Math.max(maxConsecutiveLosses, consecutiveLosses);
      } else {
        consecutiveLosses = 0;
      }
    }

    // Calculate Sharpe ratio (simplified)
    const avgReturn = profits.reduce((a, b) => a + b, 0) / profits.length;
    const variance = profits.reduce((sum, profit) => sum + Math.pow(profit - avgReturn, 2), 0) / profits.length;
    const volatility = Math.sqrt(variance);
    const sharpeRatio = volatility > 0 ? avgReturn / volatility : 0;

    // Calculate VaR 95%
    const sortedProfits = [...profits].sort((a, b) => a - b);
    const var95Index = Math.floor(sortedProfits.length * 0.05);
    const var95 = sortedProfits[var95Index] || 0;

    return {
      maxDrawdown,
      sharpeRatio,
      winRate,
      profitFactor,
      averageWin,
      averageLoss,
      consecutiveLosses: maxConsecutiveLosses,
      riskRewardRatio: averageLoss > 0 ? averageWin / averageLoss : 0,
      volatility,
      var95,
    };
  }

  calculatePositionSizing(
    settings: TradingSettings,
    marketData: MarketData,
    confidence: number
  ): PositionSizing {
    if (!this.account) {
      return {
        recommendedStake: settings.stake,
        maxStake: settings.stake,
        riskPercentage: 0.02,
        kellyPercentage: 0,
      };
    }

    const balance = this.account.balance;
    const metrics = this.calculateRiskMetrics();

    // Kelly Criterion calculation
    const winRate = metrics.winRate;
    const avgWin = metrics.averageWin;
    const avgLoss = metrics.averageLoss;
    
    let kellyPercentage = 0;
    if (avgLoss > 0 && winRate > 0) {
      kellyPercentage = (winRate * avgWin - (1 - winRate) * avgLoss) / avgWin;
      kellyPercentage = Math.max(0, Math.min(kellyPercentage, 0.25)); // Cap at 25%
    }

    // Adjust for confidence
    const confidenceMultiplier = Math.max(0.1, Math.min(confidence, 1));
    
    // Base risk percentage (2% of account)
    let riskPercentage = 0.02 * confidenceMultiplier;
    
    // Reduce risk if consecutive losses
    if (metrics.consecutiveLosses >= 3) {
      riskPercentage *= 0.5;
    }
    
    // Reduce risk if high drawdown
    if (metrics.maxDrawdown > 0.1) {
      riskPercentage *= 0.7;
    }

    const recommendedStake = Math.min(
      balance * riskPercentage,
      balance * kellyPercentage,
      settings.stake * 2 // Don't exceed 2x user setting
    );

    const maxStake = balance * 0.05; // Never risk more than 5% on single trade

    return {
      recommendedStake: Math.max(0.1, recommendedStake),
      maxStake,
      riskPercentage,
      kellyPercentage,
    };
  }

  private checkRiskAlerts(): void {
    this.riskAlerts = [];
    const metrics = this.calculateRiskMetrics();

    // Check consecutive losses
    if (metrics.consecutiveLosses >= this.maxConsecutiveLosses) {
      this.addAlert('danger', `${metrics.consecutiveLosses} consecutive losses detected. Consider reducing position size.`, 9);
    }

    // Check drawdown
    if (metrics.maxDrawdown > this.maxDrawdown) {
      this.addAlert('danger', `Maximum drawdown exceeded: ${(metrics.maxDrawdown * 100).toFixed(1)}%`, 8);
    }

    // Check win rate
    if (this.trades.length > 10 && metrics.winRate < 0.3) {
      this.addAlert('warning', `Low win rate: ${(metrics.winRate * 100).toFixed(1)}%`, 6);
    }

    // Check profit factor
    if (this.trades.length > 10 && metrics.profitFactor < 1) {
      this.addAlert('warning', `Profit factor below 1.0: ${metrics.profitFactor.toFixed(2)}`, 7);
    }

    // Check daily loss
    if (this.account) {
      const todayTrades = this.getTodayTrades();
      const todayLoss = todayTrades.reduce((sum, trade) => sum + (trade.profit || 0), 0);
      const dailyLossPercentage = Math.abs(todayLoss) / this.account.balance;
      
      if (todayLoss < 0 && dailyLossPercentage > this.maxDailyLoss) {
        this.addAlert('danger', `Daily loss limit exceeded: ${(dailyLossPercentage * 100).toFixed(1)}%`, 10);
      }
    }
  }

  private addAlert(type: RiskAlert['type'], message: string, severity: number): void {
    this.riskAlerts.push({
      type,
      message,
      severity,
      timestamp: Date.now(),
    });
  }

  private getTodayTrades(): Trade[] {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayTimestamp = today.getTime();

    return this.trades.filter(trade => trade.timestamp >= todayTimestamp);
  }

  getRiskAlerts(): RiskAlert[] {
    return this.riskAlerts.sort((a, b) => b.severity - a.severity);
  }

  shouldAllowTrade(settings: TradingSettings): { allowed: boolean; reason?: string } {
    const metrics = this.calculateRiskMetrics();

    // Check consecutive losses
    if (metrics.consecutiveLosses >= this.maxConsecutiveLosses) {
      return { allowed: false, reason: 'Too many consecutive losses' };
    }

    // Check daily loss limit
    if (this.account) {
      const todayTrades = this.getTodayTrades();
      const todayLoss = todayTrades.reduce((sum, trade) => sum + (trade.profit || 0), 0);
      const dailyLossPercentage = Math.abs(todayLoss) / this.account.balance;
      
      if (todayLoss < 0 && dailyLossPercentage > this.maxDailyLoss) {
        return { allowed: false, reason: 'Daily loss limit exceeded' };
      }
    }

    // Check if stake is within limits
    const positionSizing = this.calculatePositionSizing(settings, {} as MarketData, 0.7);
    if (settings.stake > positionSizing.maxStake) {
      return { allowed: false, reason: 'Stake exceeds maximum allowed' };
    }

    return { allowed: true };
  }

  updateSettings(settings: {
    maxConsecutiveLosses?: number;
    maxDailyLoss?: number;
    maxDrawdown?: number;
  }): void {
    if (settings.maxConsecutiveLosses !== undefined) {
      this.maxConsecutiveLosses = settings.maxConsecutiveLosses;
    }
    if (settings.maxDailyLoss !== undefined) {
      this.maxDailyLoss = settings.maxDailyLoss;
    }
    if (settings.maxDrawdown !== undefined) {
      this.maxDrawdown = settings.maxDrawdown;
    }

    // Save to localStorage
    try {
      localStorage.setItem('riskManagerSettings', JSON.stringify({
        maxConsecutiveLosses: this.maxConsecutiveLosses,
        maxDailyLoss: this.maxDailyLoss,
        maxDrawdown: this.maxDrawdown,
      }));
    } catch (error) {
      console.error('Failed to save risk manager settings:', error);
    }
  }
}

// Singleton instance
let riskManagerInstance: RiskManager | null = null;

export const getRiskManager = (): RiskManager => {
  if (!riskManagerInstance) {
    riskManagerInstance = new RiskManager();
  }
  return riskManagerInstance;
};
