import {
  AccountInfo,
  BalanceResponse,
  ChartData,
  MarketData,
  MarketSymbol,
  OHLCData,
  Trade,
  TradeStatus,
  TransactionResponse,
  TradingMode,
  TradeDirection,
  EventHandlers,
  TickData,
  Transaction,
  ProposalOpenContractResponse,
} from '../types/trading';

interface DerivResponse {
  msg_type: string;
  req_id?: number;
  echo_req?: any;
  error?: {
    code: string;
    message: string;
  };
  subscription?: {
    id: string;
  };
  [key: string]: any;
}

interface AuthorizeResponse extends DerivResponse {
  authorize: {
    email: string;
    currency: string;
    balance: number;
    landing_company_name: string;
    loginid: string;
    fullname: string;
    is_virtual?: number;
  };
}

interface BuyResponse extends DerivResponse {
  buy: {
    balance_after: number;
    buy_price: number;
    contract_id: number;
    longcode: string;
    payout: number;
    purchase_time: number;
    start_time: number;
    transaction_id: number;
  };
}

type RequestPromiseResolvers = {
  [key: number]: {
    resolve: (value: any) => void;
    reject: (reason?: any) => void;
  };
};

export class DerivAPIService {
  private ws: WebSocket | null = null;
  private apiToken: string;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private pingInterval: NodeJS.Timeout | null = null;
  private isAuthorized = false;
  private requestIdCounter = 1;

  private isConnecting = false;
  private isClosing = false;

  private active_symbols: any[] = [];
  private marketDataCache: { [symbol: string]: MarketData } = {};
  private activeSubscriptions: { [subId: string]: { request: any } } = {};

  private pendingRequests: RequestPromiseResolvers = {};

  private eventHandlers: EventHandlers = {};

  constructor(apiToken: string) {
    this.apiToken = apiToken;
    this.connect();
  }

  public setEventHandlers(handlers: EventHandlers): void {
    this.eventHandlers = handlers;
  }

  private connect(): void {
    if (this.ws && (this.ws.readyState === WebSocket.OPEN || this.ws.readyState === WebSocket.CONNECTING)) {
      return;
    }
    if (this.isConnecting) {
      return;
    }

    this.isConnecting = true;
    this.ws = new WebSocket('wss://ws.derivws.com/websockets/v3?app_id=1089');

    this.ws.onopen = async () => {
      console.log('Deriv API connected.');
      this.isConnecting = false;
      this.reconnectAttempts = 0;
      this.eventHandlers.onConnectionStatusChange?.(true, 'Connection established');
      try {
        await this.authorize();
        await this.resubscribeAfterReconnect();
        this.startPing();
      } catch (error) {
        console.error('Error during post-connection setup:', error);
        this.eventHandlers.onConnectionStatusChange?.(false, 'Authorization failed');
        this.disconnect();
      }
    };

    this.ws.onmessage = (event: MessageEvent) => {
      const data = JSON.parse(event.data);
      this.handleMessage(data);
    };

    this.ws.onclose = () => {
      console.log('Deriv API disconnected.');
      this.isConnecting = false;
      this.isAuthorized = false;
      this.stopPing();
      this.eventHandlers.onConnectionStatusChange?.(false, 'Disconnected');
      this.handleReconnect();
    };

    this.ws.onerror = (event: Event) => {
      console.error('Deriv API connection error:', event);
      this.isConnecting = false;
      this.eventHandlers.onConnectionStatusChange?.(false, 'Connection error');
    };
  }

  public disconnect(): void {
    if (this.ws) {
      this.isClosing = true;
      this.ws.close();
    }
  }

  private handleReconnect(): void {
    if (this.isClosing) return;
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);
      console.log(`Attempting to reconnect in ${delay / 1000}s...`);
      setTimeout(() => this.connect(), delay);
    } else {
      console.error('Max reconnection attempts reached.');
      this.eventHandlers.onConnectionStatusChange?.(false, 'Max reconnection attempts reached');
    }
  }

  private startPing(): void {
    this.stopPing();
    this.pingInterval = setInterval(() => {
      this.send({ ping: 1 }).catch(err => console.error('Ping failed:', err));
    }, 30000);
  }

  private stopPing(): void {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
  }

  private send(request: any): Promise<any> {
    return new Promise((resolve, reject) => {
      if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
        return reject(new Error('WebSocket is not connected.'));
      }
      const req_id = this.requestIdCounter++;
      const fullRequest = { ...request, req_id };

      this.pendingRequests[req_id] = { resolve, reject };
      this.ws.send(JSON.stringify(fullRequest));

      setTimeout(() => {
        if (this.pendingRequests[req_id]) {
          this.pendingRequests[req_id].reject(new Error(`Request ${req_id} timed out`));
          delete this.pendingRequests[req_id];
        }
      }, 15000);
    });
  }

  private handleMessage(data: DerivResponse): void {
    if (data.req_id && this.pendingRequests[data.req_id]) {
      if (data.error) {
        this.pendingRequests[data.req_id].reject(data.error);
      } else {
        this.pendingRequests[data.req_id].resolve(data);
      }
      delete this.pendingRequests[data.req_id];
      return;
    }

    switch (data.msg_type) {
      case 'authorize':
        this.handleAuthorize(data as AuthorizeResponse);
        break;
      case 'tick':
        this.handleTickResponse(data as DerivResponse & { tick: TickData });
        break;
      case 'ohlc':
        this.handleOHLCResponse(data as DerivResponse & { ohlc: OHLCData });
        break;
      case 'balance':
        this.handleBalanceResponse(data as unknown as BalanceResponse);
        break;
      case 'proposal_open_contract':
        this.handleProposalOpenContractResponse(data as unknown as ProposalOpenContractResponse);
        break;
      case 'transaction':
        this.handleTransactionResponse(data as unknown as TransactionResponse);
        break;
    }
  }

  private async authorize(): Promise<void> {
    if (!this.apiToken) {
      console.log('No API token provided. Skipping authorization.');
      this.isAuthorized = true;
      return;
    }

    const response: AuthorizeResponse = await this.send({ authorize: this.apiToken });
    if (response.error) {
      this.isAuthorized = false;
      throw new Error(response.error.message);
    }
    this.isAuthorized = true;
    this.handleAuthorize(response);

    // Subscribe to continuous balance updates so UI always reflects latest
    try {
      const balResp = await this.send({ balance: 1, subscribe: 1 });
      if (balResp.subscription && balResp.subscription.id) {
        this.activeSubscriptions[balResp.subscription.id] = { request: { balance: 1, subscribe: 1 } };
      }
    } catch (err) {
      console.error('Balance subscription failed:', err);
    }
  }

  private handleAuthorize(data: AuthorizeResponse): void {
    const accountInfo: AccountInfo = {
      id: data.authorize.loginid,
      email: data.authorize.email,
      currency: data.authorize.currency,
      balance: data.authorize.balance,
      isVirtual: !!data.authorize.is_virtual,
      name: data.authorize.fullname,
    };
    this.eventHandlers.onAccountUpdate?.(accountInfo);
  }

  private handleTickResponse(response: DerivResponse & { tick: TickData }): void {
    const tick = response.tick;
    if (!tick) return;

    const lastDigit = parseInt(tick.quote.toString().slice(-1));

    let marketData = this.marketDataCache[tick.symbol] || {
      id: tick.symbol,
      symbol: tick.symbol,
      name: this.getMarketName(tick.symbol),
      lastPrice: 0,
      timestamp: 0,
      change: 0,
      changePercent: 0,
      volume: 0,
      digits: [],
    };

    marketData.lastPrice = tick.quote;
    marketData.timestamp = tick.epoch * 1000;
    marketData.digits = [...(marketData.digits || []), lastDigit].slice(-20);

    this.marketDataCache[tick.symbol] = marketData;

    this.eventHandlers.onMarketDataUpdate?.({ ...marketData });
  }

  private handleOHLCResponse(data: DerivResponse & { ohlc: OHLCData }): void {
    const subId = data.subscription?.id;
    if (subId && this.activeSubscriptions[subId]) {
      const symbol = this.activeSubscriptions[subId].request.ticks_history;
      const candle = data.ohlc;
      const chartData: ChartData = {
          timestamp: candle.epoch * 1000,
          open: parseFloat(candle.open),
          high: parseFloat(candle.high),
          low: parseFloat(candle.low),
          close: parseFloat(candle.close),
          volume: candle.volume ? parseFloat(candle.volume) : 0,
      };
      this.eventHandlers.onChartDataUpdate?.(symbol, [chartData]);
    }
  }

  private handleBalanceResponse(data: BalanceResponse): void {
    if (data.balance) {
      const accountInfo: Partial<AccountInfo> = {
        balance: data.balance.balance,
        currency: data.balance.currency,
      };
      this.eventHandlers.onAccountUpdate?.(accountInfo as AccountInfo);
    }
  }

  private handleProposalOpenContractResponse(response: ProposalOpenContractResponse): void {
    const contract = response.proposal_open_contract;
    if (!contract) return;

    const isSold = !!contract.is_sold;
    const status =
      contract.status === 'won'
        ? TradeStatus.Won
        : contract.status === 'lost'
        ? TradeStatus.Lost
        : TradeStatus.Active;

    const profit = contract.profit ? parseFloat(String(contract.profit)) : undefined;

    const tradeUpdate: Partial<Trade> & { id: string } = {
      id: String(contract.contract_id),
      status: status,
      profit: profit,
      entryPrice: contract.entry_tick ? parseFloat(contract.entry_tick) : undefined,
      exitPrice: contract.exit_tick ? parseFloat(contract.exit_tick) : undefined,
      isSold: isSold,
    };

    this.eventHandlers.onTradeUpdate?.(tradeUpdate as Trade);

    if (isSold) {
      this.unsubscribe(String(contract.contract_id));
    }
  }

  private handleTransactionResponse(data: TransactionResponse): void {
    if (data.transaction) {
        this.eventHandlers.onTransactionUpdate?.(data.transaction as Transaction);
    }
  }

  public async subscribeToTicks(symbol: string): Promise<string> {
    const response = await this.send({ ticks: symbol, subscribe: 1 });
    const subId = response.subscription.id;
    this.activeSubscriptions[subId] = { request: { ticks: symbol, subscribe: 1 } };
    return subId;
  }

  public async getCandles(symbol: string, granularity: number = 60, count: number = 1000): Promise<ChartData[]> {
  // Fetch historical candle data (unsubscribed)
  const request = {
    ticks_history: symbol,
    end: 'latest',
    count,
    style: 'candles',
    granularity,
  };

  const response = await this.send(request);
  if (response.error) {
    throw new Error(response.error.message);
  }

  // Deriv may return candles array directly or nested
  const rawCandles = response.candles || response.history?.candles;
  if (!rawCandles || !Array.isArray(rawCandles)) {
    return [];
  }

  const chartData: ChartData[] = rawCandles.map((c: any) => ({
    timestamp: c.epoch * 1000,
    open: parseFloat(c.open),
    high: parseFloat(c.high),
    low: parseFloat(c.low),
    close: parseFloat(c.close),
    volume: c.volume ? parseFloat(c.volume) : 0,
  }));

  // Push to listeners so UI can render immediately
  this.eventHandlers.onChartDataUpdate?.(symbol, chartData);
  return chartData;
}

  /**
   * Subscribe to live OHLC (candle) updates
   */
  public async subscribeToOHLC(symbol: string, granularity: number = 60): Promise<string> {
    const request = {
      ticks_history: symbol,
      end: 'latest',
      count: 1000,
      style: 'candles',
      granularity: granularity,
      subscribe: 1,
    };
    const response = await this.send(request);
    const subId = response.subscription.id;
    this.activeSubscriptions[subId] = { request };
    return subId;
  }

  public async unsubscribe(subscriptionId: string): Promise<void> {
    if (this.activeSubscriptions[subscriptionId]) {
      await this.send({ forget: subscriptionId });
      delete this.activeSubscriptions[subscriptionId];
    }
  }

  private async resubscribeAfterReconnect(): Promise<void> {
    const oldSubscriptions = { ...this.activeSubscriptions };
    this.activeSubscriptions = {};
    for (const subId in oldSubscriptions) {
        try {
            const newSub = await this.send(oldSubscriptions[subId].request);
            this.activeSubscriptions[newSub.subscription.id] = oldSubscriptions[subId];
        } catch (error) {
            console.error('Resubscription failed for:', oldSubscriptions[subId].request, error);
        }
    }
  }

  private getMarketName(symbol: string): string {
    const market = this.active_symbols.find(s => s.symbol === symbol);
    return market ? market.display_name : symbol;
  }

  public getContractType(mode: TradingMode, direction: TradeDirection): string {
    switch (mode) {
      case 'rise_fall':
        return direction === 'up' ? 'CALL' : 'PUT';
      case 'quadrant':
        // Quadrant strategy uses binary options (CALL/PUT)
        return direction === 'up' ? 'CALL' : 'PUT';
      case 'ai':
        // AI mode also uses binary options
        return direction === 'up' ? 'CALL' : 'PUT';
      case 'over_under':
        return direction === 'over' ? 'DIGITOVER' : 'DIGITUNDER';
      case 'diff_match':
        return direction === 'diff' ? 'DIGITDIFF' : 'DIGITMATCH';
      default:
        throw new Error(`Unsupported trade mode: ${mode}`);
    }
  }

  public async placeTrade(params: {
    symbol: MarketSymbol;
    amount: number;
    duration: number;
    duration_unit: string;
    mode: TradingMode;
    direction: TradeDirection;
    prediction?: number;
  }): Promise<Trade> {
    console.log('DerivAPI placeTrade called with params:', params);

    try {
      const contractType = this.getContractType(params.mode, params.direction);
      console.log('Contract type determined:', contractType);
      const tradeParams: any = {
        buy: '1',
        price: params.amount,
        parameters: {
          amount: params.amount,
          basis: 'stake',
          contract_type: contractType,
          currency: 'USD',
          duration: params.duration,
          duration_unit: params.duration_unit,
          symbol: params.symbol,
        },
      };

      if (params.prediction !== undefined) {
        tradeParams.parameters.barrier = params.prediction;
      }

      console.log('Sending trade params to Deriv:', tradeParams);

      const response: BuyResponse = await this.send(tradeParams);
      console.log('Deriv API response:', response);

      if (response.error) {
        console.error('Deriv API error:', response.error);
        throw new Error(response.error.message);
      }

    const buy = response.buy;
    const newTrade: Trade = {
      id: String(buy.contract_id),
      timestamp: buy.purchase_time * 1000,
      symbol: params.symbol,
      market: this.getMarketName(params.symbol),
      type: params.mode,
      direction: params.direction,
      stake: params.amount,
      duration: params.duration,
      entryPrice: buy.buy_price,
      status: TradeStatus.Active,
      isSold: false,
      transactionId: String(buy.transaction_id),
    };

    if (params.prediction) {
      newTrade.prediction = params.prediction;
    }

      this.subscribeToTradeUpdates(newTrade.id);
      this.eventHandlers.onTradeUpdate?.(newTrade);
      console.log('Trade created successfully:', newTrade);
      return newTrade;
    } catch (error) {
      console.error('DerivAPI placeTrade error:', error);
      throw error;
    }
  }

  public async getOpenContract(contractId: string): Promise<any> {
    const response = await this.send({ proposal_open_contract: 1, contract_id: Number(contractId) });
    if (response.error) throw new Error(response.error.message);
    return response.proposal_open_contract;
  }

  public async subscribeToTradeUpdates(contractId: string): Promise<void> {
    const request = {
      proposal_open_contract: 1,
      contract_id: Number(contractId),
      subscribe: 1,
    };
    const response = await this.send(request);
    if (response.error) {
      console.error('Failed to subscribe to trade updates:', response.error.message);
      return;
    }
    const subId = response.subscription.id;
    this.activeSubscriptions[subId] = { request };
  }

  public async getMarketSymbols(): Promise<MarketSymbol[]> {
    if (this.active_symbols.length > 0) {
      return this.active_symbols.map(s => s.symbol);
    }
    const response = await this.send({ active_symbols: 'brief', product_type: 'basic' });
    this.active_symbols = response.active_symbols;
    return this.active_symbols.map(s => s.symbol);
  }
}

let derivAPIServiceInstance: DerivAPIService | null = null;

export const initializeDerivAPI = (apiToken: string): DerivAPIService => {
  if (!derivAPIServiceInstance) {
    derivAPIServiceInstance = new DerivAPIService(apiToken);
  }
  return derivAPIServiceInstance;
};

export const getDerivAPI = (): DerivAPIService => {
  if (!derivAPIServiceInstance) {
    throw new Error('DerivAPI not initialized. Call initializeDerivAPI first.');
  }
  return derivAPIServiceInstance;
};
