import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { Button } from './ui/button';
import { 
  QuadrantAnalysis as QuadrantAnalysisType, 
  DigitValue,
  TradeDirection 
} from '../types/trading';
import { AITradingService } from '../services/aiService';
import { useTradingStore } from '../store/tradingStore';
import { TrendingUp, TrendingDown, Target, Activity } from 'lucide-react';
import toast from 'react-hot-toast';

interface QuadrantWidgetProps {
  aiService: AITradingService;
  onTradeSignal?: (direction: TradeDirection, confidence: number) => void;
  className?: string;
}

export const QuadrantWidget: React.FC<QuadrantWidgetProps> = ({
  aiService,
  onTradeSignal,
  className = ''
}) => {
  const { currentMarket, marketData } = useTradingStore();
  const [analysis, setAnalysis] = useState<QuadrantAnalysisType | null>(null);
  const [currentDigits, setCurrentDigits] = useState<DigitValue[]>([]);
  const [lastUpdate, setLastUpdate] = useState<number>(Date.now());
  const [tickCount, setTickCount] = useState<number>(0);
  const [isAnalysisActive, setIsAnalysisActive] = useState<boolean>(false);

  useEffect(() => {
    if (marketData[currentMarket]) {
      const digits = aiService.getCurrentDigits(marketData[currentMarket]);
      setCurrentDigits(digits);

      console.log('QuadrantWidget: Market data update', {
        market: currentMarket,
        digits,
        digitStream: marketData[currentMarket].digits,
        lastPrice: marketData[currentMarket].lastPrice
      });

      // Increment tick count on each market data update
      setTickCount(prev => prev + 1);

      // Start new analysis every 5 ticks or if no analysis exists
      if (tickCount % 5 === 0 || !analysis) {
        setIsAnalysisActive(true);

        if (digits.length >= 3) { // Need at least 3 digits for proper analysis
          console.log('QuadrantWidget: Starting analysis with digits', digits);
          const quadrantAnalysis = aiService.getQuadrantAnalysis(currentMarket, marketData[currentMarket]);
          setAnalysis(quadrantAnalysis);
          setLastUpdate(Date.now());

          if (quadrantAnalysis) {
            console.log('QuadrantWidget: Analysis completed', quadrantAnalysis);
          } else {
            console.log('QuadrantWidget: Analysis returned null');
          }
        } else {
          console.log('QuadrantWidget: Not enough digits for analysis', { digits, length: digits.length });
        }
      }
    }
  }, [marketData, currentMarket, aiService, tickCount, analysis]);

  const handleTradeSignal = () => {
    if (analysis && onTradeSignal) {
      try {
        const direction: TradeDirection = analysis.recommendation.action === 'rise' ? 'rise' : 'fall';
        console.log('Quadrant Widget: Sending trade signal', { direction, confidence: analysis.confidence });
        onTradeSignal(direction, analysis.confidence);
      } catch (error) {
        console.error('Quadrant Widget: Error handling trade signal', error);
        toast.error('Failed to execute quadrant trade signal');
      }
    }
  };

  const getSignalColor = (action: string): string => {
    return action === 'rise' ? 'text-blue-600' : 'text-red-600';
  };

  const getSignalBgColor = (action: string): string => {
    return action === 'rise' ? 'bg-blue-50 border-blue-200' : 'bg-red-50 border-red-200';
  };

  const getSignalIcon = (action: string) => {
    return action === 'rise' ? TrendingUp : TrendingDown;
  };

  const getConfidenceColor = (confidence: number): string => {
    if (confidence >= 0.8) return 'text-green-600';
    if (confidence >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getQuadrantColor = (quadrant: number): string => {
    return (quadrant === 1 || quadrant === 2) ? 'text-blue-600 bg-blue-50' : 'text-red-600 bg-red-50';
  };

  const getTrendColor = (trend: string): string => {
    return trend === 'up' ? 'text-blue-600' : 'text-red-600';
  };

  const formatTime = (timestamp: number): string => {
    return new Date(timestamp).toLocaleTimeString();
  };

  if (!analysis) {
    return (
      <Card className={className}>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm flex items-center gap-2">
            <Target className="w-4 h-4" />
            Quadrant Strategy
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4 text-gray-500">
            <Activity className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">Waiting for sufficient data...</p>
            <div className="flex justify-center gap-1 mt-2">
              {currentDigits.map((digit, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {digit}
                </Badge>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const SignalIcon = getSignalIcon(analysis.recommendation.action);

  return (
    <Card className={className}>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Target className="w-4 h-4" />
            Quadrant Strategy
          </div>
          <Badge variant="outline" className="text-xs">
            {formatTime(lastUpdate)}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {/* Current Digits */}
        <div>
          <div className="flex items-center justify-between mb-1">
            <div className="text-xs text-gray-600">Current Digits:</div>
            <div className="text-xs text-gray-500">
              Tick: {tickCount} | Next Analysis: {5 - (tickCount % 5)} ticks
            </div>
          </div>
          <div className="flex gap-1">
            {currentDigits.map((digit, index) => {
              // Get quadrant for color coding
              const position = aiService.getQuadrantPosition ?
                aiService.getQuadrantPosition(digit as DigitValue, 'b') : null;

              // Determine colors based on quadrant
              let bgColor = 'bg-gray-100';
              let textColor = 'text-gray-800';
              let borderColor = 'border-gray-300';

              if (position) {
                if (position.quadrant === 1 || position.quadrant === 2) {
                  // Blue zone (Q1, Q2)
                  bgColor = 'bg-blue-50';
                  textColor = 'text-blue-700';
                  borderColor = 'border-blue-300';
                } else {
                  // Red zone (Q3, Q4)
                  bgColor = 'bg-red-50';
                  textColor = 'text-red-700';
                  borderColor = 'border-red-300';
                }
              }

              return (
                <Badge
                  key={index}
                  variant="outline"
                  className={`text-sm font-mono px-2 py-1 font-semibold ${bgColor} ${textColor} ${borderColor}`}
                >
                  {digit}
                </Badge>
              );
            })}
          </div>
        </div>

        {/* Signal */}
        <div className={`p-3 rounded-lg border-2 ${getSignalBgColor(analysis.recommendation.action)}`}>
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <SignalIcon
                className={`w-5 h-5 ${getSignalColor(analysis.recommendation.action)}`}
              />
              <span className={`font-semibold ${getSignalColor(analysis.recommendation.action)}`}>
                {analysis.recommendation.action.toUpperCase()}
              </span>
            </div>
            <Badge
              className={`${getConfidenceColor(analysis.confidence)} bg-transparent border`}
            >
              {(analysis.confidence * 100).toFixed(0)}%
            </Badge>
          </div>

          <div className={`text-xs mb-2 ${getTrendColor(analysis.sequence.trendBias)}`}>
            Trend: <span className="font-semibold">{analysis.sequence.trendBias.toUpperCase()}</span>
            {analysis.sequence.trendBias === 'up' ? ' (Blue Zone)' : ' (Red Zone)'}
          </div>
          
          {/* Confidence Bar */}
          <div className="flex items-center gap-2">
            <div className="flex-1 bg-gray-200 rounded-full h-1.5">
              <div 
                className={`h-1.5 rounded-full transition-all duration-300 ${
                  analysis.confidence >= 0.8 ? 'bg-green-500' :
                  analysis.confidence >= 0.6 ? 'bg-yellow-500' : 'bg-red-500'
                }`}
                style={{ width: `${analysis.confidence * 100}%` }}
              />
            </div>
          </div>
        </div>

        {/* Path Trace */}
        {analysis.pathTrace.length > 0 && (
          <div>
            <div className="text-xs text-gray-600 mb-1">Path:</div>
            <div className="flex items-center gap-1 text-xs">
              {analysis.pathTrace.slice(0, 4).map((step, index) => {
                // Extract quadrant number from step (format: digit(Qn))
                const quadrantMatch = step.match(/Q(\d)/);
                const quadrant = quadrantMatch ? parseInt(quadrantMatch[1]) : 1;
                const colorClass = getQuadrantColor(quadrant);

                return (
                  <React.Fragment key={index}>
                    <span className={`font-mono px-2 py-1 rounded border text-xs ${colorClass}`}>
                      {step}
                    </span>
                    {index < Math.min(analysis.pathTrace.length - 1, 3) && (
                      <span className="text-gray-400">→</span>
                    )}
                  </React.Fragment>
                );
              })}
              {analysis.pathTrace.length > 4 && (
                <span className="text-gray-400">...</span>
              )}
            </div>
          </div>
        )}

        {/* Interchanges */}
        {analysis.interchangesUsed.length > 0 && (
          <div>
            <div className="text-xs text-gray-600 mb-1">Interchanges:</div>
            <div className="space-y-1">
              {analysis.interchangesUsed.slice(0, 2).map((interchange, index) => (
                <div key={index} className="text-xs bg-yellow-50 px-2 py-1 rounded border-l-2 border-yellow-400">
                  <span className="font-mono">{interchange.original}</span>
                  <span className="mx-1">→</span>
                  <span className="font-mono">{interchange.replacement}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Action Button */}
        {analysis.confidence >= 0.6 && onTradeSignal && (
          <Button
            onClick={handleTradeSignal}
            className={`w-full font-semibold ${
              analysis.recommendation.action === 'rise'
                ? 'bg-blue-600 hover:bg-blue-700 text-white'
                : 'bg-red-600 hover:bg-red-700 text-white'
            }`}
            size="sm"
          >
            <div className="flex items-center gap-2">
              <SignalIcon className="w-4 h-4" />
              Trade {analysis.recommendation.action.toUpperCase()}
            </div>
          </Button>
        )}

        {/* Analysis Cycle Indicator */}
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>Analysis Cycle: {Math.floor(tickCount / 5) + 1}</span>
          <div className="flex gap-1">
            {Array.from({ length: 5 }, (_, i) => (
              <div
                key={i}
                className={`w-2 h-2 rounded-full ${
                  i < (tickCount % 5) ? 'bg-blue-500' : 'bg-gray-300'
                }`}
              />
            ))}
          </div>
        </div>

        {/* Analysis Summary */}
        <div className="text-xs text-gray-600 bg-gray-50 p-2 rounded">
          {analysis.recommendation.reasoning}
        </div>
      </CardContent>
    </Card>
  );
};
