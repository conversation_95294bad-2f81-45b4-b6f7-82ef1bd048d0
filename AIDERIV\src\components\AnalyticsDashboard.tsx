import React, { useState, useEffect } from 'react';
import { 
  LineChart, Line, AreaChart, Area, BarChart, Bar, PieChart, Pie, Cell,
  XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer 
} from 'recharts';
import { 
  TrendingUp, TrendingDown, DollarSign, Target, AlertTriangle, 
  Activity, BarChart3, PieChart as PieChartIcon, Calendar, Clock 
} from 'lucide-react';
import { useTradingStore } from '../store/tradingStore';
import { getRiskManager, RiskMetrics } from '../services/riskManager';

interface AnalyticsProps {
  className?: string;
}

interface PerformanceData {
  date: string;
  profit: number;
  cumulative: number;
  trades: number;
  winRate: number;
}

interface MetricCard {
  title: string;
  value: string;
  change: string;
  trend: 'up' | 'down' | 'neutral';
  icon: React.ReactNode;
  color: string;
}

export const AnalyticsDashboard: React.FC<AnalyticsProps> = ({ className = '' }) => {
  const { trades, account } = useTradingStore();
  const [timeframe, setTimeframe] = useState<'1D' | '1W' | '1M' | '3M' | 'ALL'>('1W');
  const [riskMetrics, setRiskMetrics] = useState<RiskMetrics | null>(null);
  const [performanceData, setPerformanceData] = useState<PerformanceData[]>([]);

  useEffect(() => {
    const riskManager = getRiskManager();
    riskManager.updateTrades(trades);
    if (account) {
      riskManager.updateAccount(account);
    }
    setRiskMetrics(riskManager.calculateRiskMetrics());
    generatePerformanceData();
  }, [trades, account, timeframe]);

  const generatePerformanceData = () => {
    const completedTrades = trades.filter(t => t.status === 'completed');
    if (completedTrades.length === 0) {
      setPerformanceData([]);
      return;
    }

    // Group trades by date
    const tradesByDate = new Map<string, typeof completedTrades>();
    completedTrades.forEach(trade => {
      const date = new Date(trade.timestamp).toISOString().split('T')[0];
      if (!tradesByDate.has(date)) {
        tradesByDate.set(date, []);
      }
      tradesByDate.get(date)!.push(trade);
    });

    let cumulativeProfit = 0;
    const data: PerformanceData[] = [];

    Array.from(tradesByDate.entries())
      .sort(([a], [b]) => a.localeCompare(b))
      .forEach(([date, dayTrades]) => {
        const dayProfit = dayTrades.reduce((sum, trade) => sum + (trade.profit || 0), 0);
        const wins = dayTrades.filter(trade => (trade.profit || 0) > 0).length;
        const winRate = dayTrades.length > 0 ? (wins / dayTrades.length) * 100 : 0;
        
        cumulativeProfit += dayProfit;
        
        data.push({
          date,
          profit: dayProfit,
          cumulative: cumulativeProfit,
          trades: dayTrades.length,
          winRate,
        });
      });

    setPerformanceData(data);
  };

  const getMetricCards = (): MetricCard[] => {
    if (!riskMetrics) return [];

    const totalProfit = trades.reduce((sum, trade) => sum + (trade.profit || 0), 0);
    const totalTrades = trades.filter(t => t.status === 'completed').length;
    const avgProfit = totalTrades > 0 ? totalProfit / totalTrades : 0;

    return [
      {
        title: 'Total Profit',
        value: `$${totalProfit.toFixed(2)}`,
        change: totalProfit >= 0 ? '+' : '',
        trend: totalProfit >= 0 ? 'up' : 'down',
        icon: <DollarSign className="w-5 h-5" />,
        color: totalProfit >= 0 ? 'text-green-500' : 'text-red-500',
      },
      {
        title: 'Win Rate',
        value: `${(riskMetrics.winRate * 100).toFixed(1)}%`,
        change: riskMetrics.winRate >= 0.5 ? 'Good' : 'Poor',
        trend: riskMetrics.winRate >= 0.5 ? 'up' : 'down',
        icon: <Target className="w-5 h-5" />,
        color: riskMetrics.winRate >= 0.5 ? 'text-green-500' : 'text-red-500',
      },
      {
        title: 'Profit Factor',
        value: riskMetrics.profitFactor.toFixed(2),
        change: riskMetrics.profitFactor >= 1 ? 'Profitable' : 'Loss',
        trend: riskMetrics.profitFactor >= 1 ? 'up' : 'down',
        icon: <TrendingUp className="w-5 h-5" />,
        color: riskMetrics.profitFactor >= 1 ? 'text-green-500' : 'text-red-500',
      },
      {
        title: 'Max Drawdown',
        value: `${(riskMetrics.maxDrawdown * 100).toFixed(1)}%`,
        change: riskMetrics.maxDrawdown <= 0.1 ? 'Low Risk' : 'High Risk',
        trend: riskMetrics.maxDrawdown <= 0.1 ? 'up' : 'down',
        icon: <TrendingDown className="w-5 h-5" />,
        color: riskMetrics.maxDrawdown <= 0.1 ? 'text-green-500' : 'text-red-500',
      },
      {
        title: 'Sharpe Ratio',
        value: riskMetrics.sharpeRatio.toFixed(2),
        change: riskMetrics.sharpeRatio >= 1 ? 'Excellent' : 'Poor',
        trend: riskMetrics.sharpeRatio >= 1 ? 'up' : 'down',
        icon: <Activity className="w-5 h-5" />,
        color: riskMetrics.sharpeRatio >= 1 ? 'text-green-500' : 'text-red-500',
      },
      {
        title: 'Total Trades',
        value: totalTrades.toString(),
        change: `Avg: $${avgProfit.toFixed(2)}`,
        trend: 'neutral',
        icon: <BarChart3 className="w-5 h-5" />,
        color: 'text-blue-500',
      },
    ];
  };

  const getTradeDistribution = () => {
    const modeCount = trades.reduce((acc, trade) => {
      acc[trade.mode] = (acc[trade.mode] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(modeCount).map(([mode, count]) => ({
      name: mode.replace('_', ' ').toUpperCase(),
      value: count,
      color: {
        ai: '#3B82F6',
        over_under: '#10B981',
        diff_match: '#F59E0B',
        rise_fall: '#EF4444',
      }[mode] || '#6B7280',
    }));
  };

  const metricCards = getMetricCards();
  const tradeDistribution = getTradeDistribution();

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white">Analytics Dashboard</h2>
        <div className="flex space-x-2">
          {(['1D', '1W', '1M', '3M', 'ALL'] as const).map((period) => (
            <button
              key={period}
              onClick={() => setTimeframe(period)}
              className={`px-3 py-1 rounded text-sm transition-colors ${
                timeframe === period
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              {period}
            </button>
          ))}
        </div>
      </div>

      {/* Metric Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {metricCards.map((metric, index) => (
          <div key={index} className="bg-gray-800 rounded-lg p-4 border border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">{metric.title}</p>
                <p className={`text-2xl font-bold ${metric.color}`}>{metric.value}</p>
                <p className="text-xs text-gray-500">{metric.change}</p>
              </div>
              <div className={`p-2 rounded-lg bg-gray-700 ${metric.color}`}>
                {metric.icon}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Performance Chart */}
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <h3 className="text-lg font-semibold text-white mb-4">Performance Over Time</h3>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={performanceData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
              <XAxis dataKey="date" stroke="#9CA3AF" />
              <YAxis stroke="#9CA3AF" />
              <Tooltip 
                contentStyle={{ 
                  backgroundColor: '#1F2937', 
                  border: '1px solid #374151',
                  borderRadius: '8px',
                  color: '#F9FAFB'
                }} 
              />
              <Area 
                type="monotone" 
                dataKey="cumulative" 
                stroke="#3B82F6" 
                fill="#3B82F6" 
                fillOpacity={0.3}
                name="Cumulative Profit"
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>

        {/* Trade Distribution */}
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <h3 className="text-lg font-semibold text-white mb-4">Trade Distribution</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={tradeDistribution}
                cx="50%"
                cy="50%"
                outerRadius={100}
                dataKey="value"
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
              >
                {tradeDistribution.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip 
                contentStyle={{ 
                  backgroundColor: '#1F2937', 
                  border: '1px solid #374151',
                  borderRadius: '8px',
                  color: '#F9FAFB'
                }} 
              />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Daily Performance Chart */}
      <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">Daily Performance</h3>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={performanceData}>
            <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
            <XAxis dataKey="date" stroke="#9CA3AF" />
            <YAxis stroke="#9CA3AF" />
            <Tooltip 
              contentStyle={{ 
                backgroundColor: '#1F2937', 
                border: '1px solid #374151',
                borderRadius: '8px',
                color: '#F9FAFB'
              }} 
            />
            <Bar 
              dataKey="profit" 
              fill="#3B82F6"
              name="Daily Profit"
            />
          </BarChart>
        </ResponsiveContainer>
      </div>

      {/* Risk Alerts */}
      {riskMetrics && (
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <AlertTriangle className="w-5 h-5 mr-2 text-yellow-500" />
            Risk Analysis
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-gray-400 text-sm">Consecutive Losses</p>
              <p className={`text-xl font-bold ${riskMetrics.consecutiveLosses >= 3 ? 'text-red-500' : 'text-green-500'}`}>
                {riskMetrics.consecutiveLosses}
              </p>
            </div>
            <div className="text-center">
              <p className="text-gray-400 text-sm">Risk/Reward Ratio</p>
              <p className={`text-xl font-bold ${riskMetrics.riskRewardRatio >= 1 ? 'text-green-500' : 'text-red-500'}`}>
                {riskMetrics.riskRewardRatio.toFixed(2)}
              </p>
            </div>
            <div className="text-center">
              <p className="text-gray-400 text-sm">Volatility</p>
              <p className="text-xl font-bold text-blue-500">
                {riskMetrics.volatility.toFixed(2)}
              </p>
            </div>
            <div className="text-center">
              <p className="text-gray-400 text-sm">VaR 95%</p>
              <p className="text-xl font-bold text-orange-500">
                ${riskMetrics.var95.toFixed(2)}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
