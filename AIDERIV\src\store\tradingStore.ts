import { create } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';
import {
  MarketData,
  ChartData,
  Trade,
  AccountInfo,
  TradingSettings,
  AISignal,
  MarketSymbol,
  TradingMode,
  MarketCondition,
  NotificationConfig,
  ThemeConfig,
} from '../types/trading';

interface TradingState {
  // Market Data
  currentMarket: MarketSymbol;
  marketData: Record<string, MarketData>;
  chartData: Record<string, ChartData[]>;
  isConnected: boolean;
  connectionMessage: string;
  
  // Trading
  currentMode: TradingMode;
  trades: Trade[];
  activeTrades: Trade[];
  settings: Record<TradingMode, TradingSettings>;
  
  // Account
  account: AccountInfo | null;
  
  // AI & Signals
  aiSignals: AISignal[];
  marketConditions: Record<string, MarketCondition>;
  
  // UI State
  theme: ThemeConfig;
  notifications: NotificationConfig;
  sidebarOpen: boolean;
  
  // Actions
  setCurrentMarket: (market: MarketSymbol) => void;
  updateMarketData: (symbol: string, data: MarketData) => void;
  updateChartData: (symbol: string, data: ChartData[]) => void;
  setConnectionStatus: (connected: boolean, message?: string) => void;
  
  setCurrentMode: (mode: TradingMode) => void;
  addTrade: (trade: Trade) => void;
  updateTrade: (id: string, updates: Partial<Trade>) => void;
  updateSettings: (mode: TradingMode, settings: Partial<TradingSettings>) => void;
  
  setAccount: (account: AccountInfo) => void;
  updateAccount: (updates: Partial<AccountInfo>) => void;
  
  addAISignal: (signal: AISignal) => void;
  clearOldSignals: () => void;
  updateMarketCondition: (symbol: string, condition: MarketCondition) => void;
  
  setTheme: (theme: Partial<ThemeConfig>) => void;
  setNotifications: (notifications: Partial<NotificationConfig>) => void;
  setSidebarOpen: (open: boolean) => void;
}

const defaultSettings: TradingSettings = {
  stake: 1,
  duration: 5,
  targetProfit: 10,
  stopLoss: 50,
  prediction: 5,
  autoTrading: false,
  riskLevel: 'medium',
};

const defaultTheme: ThemeConfig = {
  mode: 'dark',
  primaryColor: '#3b82f6',
  accentColor: '#10b981',
  fontSize: 'medium',
};

const defaultNotifications: NotificationConfig = {
  sound: true,
  desktop: true,
  email: false,
  trades: true,
  signals: true,
  profits: true,
};

export const useTradingStore = create<TradingState>()(
  devtools(
    subscribeWithSelector((set, get) => ({
      // Initial state
      currentMarket: 'R_100',
      marketData: {},
      chartData: {},
      isConnected: false,
      connectionMessage: 'Not connected',
      
      currentMode: 'ai',
      trades: [],
      activeTrades: [],
      settings: {
        ai: { ...defaultSettings },
        over_under: { ...defaultSettings },
        diff_match: { ...defaultSettings },
        rise_fall: { ...defaultSettings },
        quadrant: { ...defaultSettings },
      },
      
      account: null,
      
      aiSignals: [],
      marketConditions: {},
      
      theme: defaultTheme,
      notifications: defaultNotifications,
      sidebarOpen: true,
      
      // Actions
      setCurrentMarket: (market) => set({ currentMarket: market }),
      
      updateMarketData: (symbol, data) =>
        set((state) => ({
          marketData: { ...state.marketData, [symbol]: data },
        })),
      
      updateChartData: (symbol, data) =>
        set((state) => ({
          chartData: { ...state.chartData, [symbol]: data },
        })),
      
      setConnectionStatus: (connected, message) => set({ 
        isConnected: connected,
        connectionMessage: message || (connected ? 'Connected' : 'Not connected') 
      }),
      
      setCurrentMode: (mode) => set({ currentMode: mode }),
      
      addTrade: (trade) =>
        set((state) => ({
          trades: [trade, ...state.trades],
          activeTrades: trade.status === 'active' ? [trade, ...state.activeTrades] : state.activeTrades,
        })),
      
      updateTrade: (id, updates) =>
        set((state) => ({
          trades: state.trades.map((trade) =>
            trade.id === id ? { ...trade, ...updates } : trade
          ),
          activeTrades: state.activeTrades.map((trade) =>
            trade.id === id ? { ...trade, ...updates } : trade
          ).filter(trade => trade.status === 'active'),
        })),
      
      updateSettings: (mode, settings) =>
        set((state) => ({
          settings: {
            ...state.settings,
            [mode]: { ...state.settings[mode], ...settings },
          },
        })),
      
      setAccount: (account) => set({ account }),
      
      updateAccount: (updates) =>
        set((state) => ({
          account: state.account ? { ...state.account, ...updates } : null,
        })),
      
      addAISignal: (signal) =>
        set((state) => ({
          aiSignals: [signal, ...state.aiSignals.slice(0, 49)], // Keep last 50 signals
        })),
      
      clearOldSignals: () =>
        set((state) => ({
          aiSignals: state.aiSignals.filter(
            (signal) => Date.now() - signal.timestamp < signal.validity * 1000
          ),
        })),
      
      updateMarketCondition: (symbol, condition) =>
        set((state) => ({
          marketConditions: { ...state.marketConditions, [symbol]: condition },
        })),
      
      setTheme: (theme) =>
        set((state) => ({
          theme: { ...state.theme, ...theme },
        })),
      
      setNotifications: (notifications) =>
        set((state) => ({
          notifications: { ...state.notifications, ...notifications },
        })),
      
      setSidebarOpen: (open) => set({ sidebarOpen: open }),
    })),
    { name: 'trading-store' }
  )
);

// Selectors
export const useMarketData = (symbol?: string) => 
  useTradingStore((state) => symbol ? state.marketData[symbol] : state.marketData);

export const useChartData = (symbol?: string) => 
  useTradingStore((state) => symbol ? state.chartData[symbol] || [] : state.chartData);

export const useCurrentSettings = () => 
  useTradingStore((state) => state.settings[state.currentMode]);

export const useLatestSignals = (limit = 10) => 
  useTradingStore((state) => state.aiSignals.slice(0, limit));
