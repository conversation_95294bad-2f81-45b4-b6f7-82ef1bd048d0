import { ChartData, MarketData, AISignal, AccountInfo, Trade, TradeStatus } from '../types/trading';

export class MockDataService {
  private priceBase = 1000;
  private dataPoints: ChartData[] = [];
  private currentPrice = this.priceBase;
  private marketData: MarketData | null = null;

  constructor() {
    this.generateInitialData();
  }

  private generateInitialData(): void {
    const now = Date.now();
    
    // Generate 100 historical data points
    for (let i = 99; i >= 0; i--) {
      const timestamp = now - (i * 60000); // 1 minute intervals
      const change = (Math.random() - 0.5) * 4; // Random price movement
      this.currentPrice += change;
      
      const open = this.currentPrice;
      const high = open + Math.random() * 2;
      const low = open - Math.random() * 2;
      const close = low + Math.random() * (high - low);
      
      this.dataPoints.push({
        timestamp,
        open,
        high,
        low,
        close,
        volume: Math.floor(Math.random() * 1000) + 100,
      });
      
      this.currentPrice = close;
    }
    
    this.updateMarketData();
  }

  private updateMarketData(): void {
    if (this.dataPoints.length === 0) return;
    
    const latest = this.dataPoints[this.dataPoints.length - 1];
    const previous = this.dataPoints[this.dataPoints.length - 2];
    const change = latest.close - previous.close;
    const changePercent = (change / previous.close) * 100;
    
    // Generate last digit
    const lastDigit = parseInt(latest.close.toFixed(4).slice(-1));
    
    this.marketData = {
      id: 'R_100',
      symbol: 'R_100',
      name: 'Volatility 100 Index',
      lastPrice: latest.close,
      change,
      changePercent,
      timestamp: latest.timestamp,
      digits: [lastDigit],
      volume: latest.volume,
    };
  }

  getChartData(): ChartData[] {
    return [...this.dataPoints];
  }

  getCurrentMarketData(): MarketData | null {
    return this.marketData;
  }

  generateNewTick(): void {
    const lastPoint = this.dataPoints[this.dataPoints.length - 1];
    const change = (Math.random() - 0.5) * 3;
    const newPrice = lastPoint.close + change;
    
    const newPoint: ChartData = {
      timestamp: Date.now(),
      open: lastPoint.close,
      high: Math.max(lastPoint.close, newPrice) + Math.random() * 0.5,
      low: Math.min(lastPoint.close, newPrice) - Math.random() * 0.5,
      close: newPrice,
      volume: Math.floor(Math.random() * 1000) + 100,
    };
    
    this.dataPoints.push(newPoint);
    if (this.dataPoints.length > 200) {
      this.dataPoints.shift(); // Keep only last 200 points
    }
    
    this.updateMarketData();
  }

  generateAISignal(): AISignal {
    const directions = ['up', 'down', 'over', 'under'] as const;
    const direction = directions[Math.floor(Math.random() * directions.length)];
    const confidence = 0.6 + Math.random() * 0.35; // 60-95% confidence
    
    const reasons = [
      'RSI indicating oversold conditions',
      'Bullish divergence detected',
      'Support level bounce pattern',
      'Moving average crossover signal',
      'Volume spike confirming trend',
      'Fibonacci retracement level',
      'Technical pattern breakout',
      'Market momentum shift detected',
    ];
    
    return {
      id: `signal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      symbol: 'R_100',
      direction,
      confidence,
      reason: reasons[Math.floor(Math.random() * reasons.length)],
      type: 'ai',
      prediction: Math.floor(Math.random() * 10),
      validity: 300,
    };
  }

  generateMockAccount(): AccountInfo {
    return {
      id: 'demo_account',
      currency: 'USD',
      balance: 10000.00,
      totalProfit: 2450.75,
      totalLoss: 1850.25,
      winRate: 68.5,
      tradeCount: 157,
    };
  }

  generateMockTrade(): Trade {
    const types = ['ai', 'over_under', 'diff_match', 'rise_fall', 'quadrant'] as const;
    const directions = ['up', 'down', 'over', 'under', 'diff', 'match'] as const;

    const type = types[Math.floor(Math.random() * types.length)];
    const direction = directions[Math.floor(Math.random() * directions.length)];
    const stake = Math.floor(Math.random() * 50) + 5;

    return {
      id: `trade_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      symbol: 'R_100',
      market: 'R_100',
      type,
      direction,
      stake,
      duration: Math.floor(Math.random() * 10) + 1, // seconds until resolution
      prediction: Math.floor(Math.random() * 10),
      entryPrice: this.currentPrice + (Math.random() - 0.5) * 10,
      status: TradeStatus.Active,
    } as Trade;
  }

  // Add demo trade execution method
  placeTrade(params: {
    symbol: string;
    amount: number;
    duration: number;
    duration_unit: string;
    mode: string;
    direction: string;
    prediction?: number;
  }): Promise<Trade> {
    return new Promise((resolve) => {
      // Simulate API delay
      setTimeout(() => {
        const trade: Trade = {
          id: `demo_trade_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          timestamp: Date.now(),
          symbol: params.symbol as any,
          market: params.symbol,
          type: params.mode as any,
          direction: params.direction as any,
          stake: params.amount,
          duration: params.duration,
          prediction: params.prediction,
          entryPrice: this.currentPrice,
          status: TradeStatus.Active,
          isSold: false,
          transactionId: `demo_tx_${Date.now()}`,
        };

        console.log('Demo trade placed:', trade);
        resolve(trade);
      }, 500); // 500ms delay to simulate API call
    });
  }
}

// Singleton instance
let mockDataInstance: MockDataService | null = null;

export const getMockDataService = (): MockDataService => {
  if (!mockDataInstance) {
    mockDataInstance = new MockDataService();
  }
  return mockDataInstance;
};
