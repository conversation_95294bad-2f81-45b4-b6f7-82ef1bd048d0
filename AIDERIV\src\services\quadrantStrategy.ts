import {
  QuadrantPosition,
  DigitMovement,
  QuadrantSequence,
  QuadrantAnalysis,
  QuadrantChart,
  MovementScale,
  DigitValue,
  QuadrantNumber,
  RankLevel,
  BlueRedScale,
  AISignal,
  TradeDirection
} from '../types/trading';

export class QuadrantStrategyService {
  private readonly quadrantChart: QuadrantChart;
  private readonly movementScale: MovementScale;

  constructor() {
    // Initialize the Master Quadrant Chart
    this.quadrantChart = {
      Q1: {
        highest: 5,
        '2nd_highest': 4,
        middle: 3,
        '2nd_lowest': 2,
        lowest: 1
      },
      Q2: {
        highest: 9,
        '2nd_highest': 8,
        middle: 7,
        '2nd_lowest': 6,
        lowest: 5
      },
      Q3: {
        highest: 1,
        '2nd_highest': 2,
        middle: 3,
        '2nd_lowest': 4,
        lowest: 5
      },
      Q4: {
        highest: 5,
        '2nd_highest': 6,
        middle: 7,
        '2nd_lowest': 8,
        lowest: 9
      }
    };

    // Initialize the Movement Analysis Scale
    this.movementScale = {
      blue: { 0: 0, 1: 1, 2: 2, 3: 3, 4: 4, 5: 5, 6: 6, 7: 7, 8: 8, 9: 9, 10: 10 },
      red: { 0: 10, 1: 9, 2: 8, 3: 7, 4: 6, 5: 5, 6: 4, 7: 3, 8: 2, 9: 1, 10: 0 }
    };
  }

  /**
   * Main analysis function for quadrant strategy
   */
  public analyzeSequence(digits: DigitValue[]): QuadrantAnalysis {
    if (digits.length < 3) {
      throw new Error('Quadrant analysis requires at least 3 digits');
    }

    // Step 1: Establish trend bias from first 2 digits
    const trendBias = this.establishTrendBias(digits[0], digits[1]);
    
    // Step 2: Create initial sequence
    const sequence = this.createSequence(digits, trendBias);
    
    // Step 3: Check for contradictions and resolve if needed
    const resolvedSequence = this.resolveContradictions(sequence);
    
    // Step 4: Calculate confidence and create analysis
    const confidence = this.calculateConfidence(resolvedSequence);
    
    // Step 5: Generate recommendation
    const recommendation = this.generateRecommendation(resolvedSequence);

    return {
      sequence: resolvedSequence,
      confidence,
      reason: this.generateReason(resolvedSequence),
      interchangesUsed: this.getInterchangesUsed(sequence, resolvedSequence),
      pathTrace: this.generatePathTrace(resolvedSequence),
      recommendation
    };
  }

  /**
   * Get quadrant position for a digit
   */
  public getQuadrantPosition(digit: DigitValue, scale: BlueRedScale = 'b'): QuadrantPosition {
    // Find which quadrant and rank this digit belongs to
    for (const [quadrantKey, ranks] of Object.entries(this.quadrantChart)) {
      for (const [rankKey, value] of Object.entries(ranks)) {
        if (value === digit) {
          const quadrant = parseInt(quadrantKey.substring(1)) as QuadrantNumber;
          const rank = rankKey as RankLevel;
          const scaleValue = scale === 'b' ? digit : this.convertToRed(digit);
          
          return {
            quadrant,
            rank,
            value: digit,
            scale,
            scaleValue
          };
        }
      }
    }

    // Handle digit 0 (special case)
    if (digit === 0) {
      return {
        quadrant: 1, // 0 typically starts in Q1
        rank: 'lowest',
        value: 0,
        scale,
        scaleValue: scale === 'b' ? 0 : 10
      };
    }

    throw new Error(`Invalid digit: ${digit}`);
  }

  /**
   * Convert blue scale to red scale
   */
  public convertToRed(blueValue: number): number {
    return 10 - blueValue;
  }

  /**
   * Convert red scale to blue scale
   */
  public convertToBlue(redValue: number): number {
    return 10 - redValue;
  }

  /**
   * Check if movement between quadrants is legal (clockwise)
   */
  public isLegalMovement(fromQuadrant: QuadrantNumber, toQuadrant: QuadrantNumber): boolean {
    // Same quadrant is always legal
    if (fromQuadrant === toQuadrant) return true;
    
    // Clockwise movement: Q1 → Q2 → Q3 → Q4 → Q1
    const clockwiseMap: Record<QuadrantNumber, QuadrantNumber> = {
      1: 2,
      2: 3,
      3: 4,
      4: 1
    };

    return clockwiseMap[fromQuadrant] === toQuadrant;
  }

  /**
   * Find equivalent digits using positional equivalence
   */
  public findEquivalentDigits(digit: DigitValue, targetQuadrant: QuadrantNumber): DigitValue[] {
    const originalPosition = this.getQuadrantPosition(digit);
    const equivalents: DigitValue[] = [];

    // Find digits with same rank in target quadrant
    const targetRanks = this.quadrantChart[`Q${targetQuadrant}` as keyof QuadrantChart];
    const targetDigit = targetRanks[originalPosition.rank];
    
    if (targetDigit !== undefined) {
      equivalents.push(targetDigit);
    }

    return equivalents;
  }

  /**
   * Establish initial trend bias from first two digits
   */
  private establishTrendBias(digit1: DigitValue, digit2: DigitValue): 'up' | 'down' {
    const pos1 = this.getQuadrantPosition(digit1, 'b');
    const pos2 = this.getQuadrantPosition(digit2, 'b');

    if (pos2.scaleValue > pos1.scaleValue) {
      return 'up';
    } else {
      return 'down';
    }
  }

  /**
   * Create sequence with movements
   */
  private createSequence(digits: DigitValue[], trendBias: 'up' | 'down'): QuadrantSequence {
    const movements: DigitMovement[] = [];
    const scale: BlueRedScale = trendBias === 'up' ? 'b' : 'r';
    
    for (let i = 0; i < digits.length - 1; i++) {
      const fromPos = this.getQuadrantPosition(digits[i], scale);
      const toPos = this.getQuadrantPosition(digits[i + 1], scale);
      
      const direction = toPos.scaleValue > fromPos.scaleValue ? 'up' : 'down';
      const isClockwise = this.isLegalMovement(fromPos.quadrant, toPos.quadrant);
      
      movements.push({
        from: fromPos,
        to: toPos,
        direction,
        isLegal: isClockwise,
        isClockwise
      });
    }

    const hasContradiction = this.checkForContradictions(movements, trendBias);
    
    return {
      digits,
      movements,
      trendBias,
      isConsistent: !hasContradiction,
      hasContradiction
    };
  }

  /**
   * Check for contradictions in movement sequence
   */
  private checkForContradictions(movements: DigitMovement[], expectedTrend: 'up' | 'down'): boolean {
    for (const movement of movements) {
      if (movement.direction !== expectedTrend) {
        return true;
      }
      if (!movement.isLegal) {
        return true;
      }
    }
    return false;
  }

  /**
   * Resolve contradictions using interchange rules
   */
  private resolveContradictions(sequence: QuadrantSequence): QuadrantSequence {
    if (!sequence.hasContradiction) {
      return sequence;
    }

    // Try to resolve using positional equivalence
    const resolvedMovements: DigitMovement[] = [];
    const scale: BlueRedScale = sequence.trendBias === 'up' ? 'b' : 'r';

    for (let i = 0; i < sequence.movements.length; i++) {
      const movement = sequence.movements[i];
      
      if (!movement.isLegal || movement.direction !== sequence.trendBias) {
        // Try to find equivalent digit that makes movement legal
        const equivalents = this.findEquivalentDigits(
          sequence.digits[i + 1], 
          this.getNextClockwiseQuadrant(movement.from.quadrant)
        );
        
        if (equivalents.length > 0) {
          const newToPos = this.getQuadrantPosition(equivalents[0], scale);
          const newDirection = newToPos.scaleValue > movement.from.scaleValue ? 'up' : 'down';
          
          resolvedMovements.push({
            from: movement.from,
            to: newToPos,
            direction: newDirection,
            isLegal: true,
            isClockwise: true
          });
        } else {
          resolvedMovements.push(movement);
        }
      } else {
        resolvedMovements.push(movement);
      }
    }

    return {
      ...sequence,
      movements: resolvedMovements,
      resolvedPath: resolvedMovements,
      isConsistent: true,
      hasContradiction: false,
      finalDirection: this.determineFinalDirection(resolvedMovements)
    };
  }

  /**
   * Get next clockwise quadrant
   */
  private getNextClockwiseQuadrant(current: QuadrantNumber): QuadrantNumber {
    const clockwiseMap: Record<QuadrantNumber, QuadrantNumber> = {
      1: 2,
      2: 3,
      3: 4,
      4: 1
    };
    return clockwiseMap[current];
  }

  /**
   * Determine final direction from resolved movements
   */
  private determineFinalDirection(movements: DigitMovement[]): 'rise' | 'fall' {
    if (movements.length === 0) return 'rise';
    
    const lastMovement = movements[movements.length - 1];
    return lastMovement.direction === 'up' ? 'rise' : 'fall';
  }

  /**
   * Calculate confidence based on sequence consistency
   */
  private calculateConfidence(sequence: QuadrantSequence): number {
    let confidence = 0.5; // Base confidence
    
    if (sequence.isConsistent) {
      confidence += 0.3;
    }
    
    if (!sequence.hasContradiction) {
      confidence += 0.2;
    }
    
    // Bonus for all legal movements
    const allLegal = sequence.movements.every(m => m.isLegal);
    if (allLegal) {
      confidence += 0.15;
    }
    
    return Math.min(confidence, 0.95);
  }

  /**
   * Generate recommendation
   */
  private generateRecommendation(sequence: QuadrantSequence): {
    action: 'rise' | 'fall';
    confidence: number;
    reasoning: string;
  } {
    const action = sequence.finalDirection || 'rise';
    const confidence = this.calculateConfidence(sequence);
    
    let reasoning = `Quadrant analysis shows ${action.toUpperCase()} signal. `;
    reasoning += `Trend bias: ${sequence.trendBias.toUpperCase()}. `;
    
    if (sequence.isConsistent) {
      reasoning += 'Path is consistent with no contradictions.';
    } else {
      reasoning += 'Path resolved using positional equivalence rules.';
    }
    
    return { action, confidence, reasoning };
  }

  /**
   * Generate analysis reason
   */
  private generateReason(sequence: QuadrantSequence): string {
    const reasons: string[] = [];
    
    reasons.push(`Initial trend bias: ${sequence.trendBias.toUpperCase()}`);
    
    if (sequence.hasContradiction) {
      reasons.push('Contradictions detected and resolved using interchange rules');
    }
    
    reasons.push(`Final direction: ${sequence.finalDirection?.toUpperCase() || 'UNKNOWN'}`);
    
    const legalMovements = sequence.movements.filter(m => m.isLegal).length;
    reasons.push(`${legalMovements}/${sequence.movements.length} movements are clockwise-legal`);
    
    return reasons.join(', ');
  }

  /**
   * Get interchanges used during resolution
   */
  private getInterchangesUsed(original: QuadrantSequence, resolved: QuadrantSequence): Array<{
    original: DigitValue;
    replacement: DigitValue;
    reason: string;
  }> {
    const interchanges: Array<{
      original: DigitValue;
      replacement: DigitValue;
      reason: string;
    }> = [];
    
    // Compare original and resolved movements to find interchanges
    for (let i = 0; i < original.movements.length; i++) {
      const origMovement = original.movements[i];
      const resolvedMovement = resolved.movements[i];
      
      if (origMovement.to.value !== resolvedMovement.to.value) {
        interchanges.push({
          original: origMovement.to.value,
          replacement: resolvedMovement.to.value,
          reason: `Positional equivalence: ${origMovement.to.rank} rank interchange`
        });
      }
    }
    
    return interchanges;
  }

  /**
   * Generate path trace for visualization
   */
  private generatePathTrace(sequence: QuadrantSequence): string[] {
    const trace: string[] = [];
    
    if (sequence.movements.length > 0) {
      // Add starting position
      const firstMovement = sequence.movements[0];
      trace.push(`${firstMovement.from.value}(Q${firstMovement.from.quadrant})`);
      
      // Add each movement
      for (const movement of sequence.movements) {
        trace.push(`${movement.to.value}(Q${movement.to.quadrant})`);
      }
    }
    
    return trace;
  }

  /**
   * Generate AI signal from quadrant analysis
   */
  public generateSignal(symbol: string, digits: DigitValue[]): AISignal | null {
    try {
      const analysis = this.analyzeSequence(digits);
      
      if (analysis.confidence < 0.6) {
        return null;
      }

      const direction: TradeDirection = analysis.recommendation.action === 'rise' ? 'rise' : 'fall';

      return {
        id: `quadrant_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: Date.now(),
        symbol,
        direction,
        confidence: analysis.confidence,
        reason: analysis.reason,
        type: 'quadrant',
        validity: 300 // 5 minutes validity
      };
    } catch (error) {
      console.error('Quadrant analysis failed:', error);
      return null;
    }
  }
}
