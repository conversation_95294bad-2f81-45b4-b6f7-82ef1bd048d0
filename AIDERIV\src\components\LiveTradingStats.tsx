import React, { useState, useEffect } from 'react';
import { 
  TrendingUp, TrendingDown, DollarSign, Target, 
  AlertTriangle, Activity, Clock, BarChart3,
  Shield, Zap, Eye, RefreshCw
} from 'lucide-react';
import { useTrading } from '../hooks/useTrading';
import { useTradingStore } from '../store/tradingStore';

interface LiveStats {
  totalTrades: number;
  wonTrades: number;
  lostTrades: number;
  activeTrades: number;
  winRate: number;
  totalProfit: number;
  totalLoss: number;
  netProfit: number;
  avgTradeTime: number;
  profitFactor: number;
  maxDrawdown: number;
  currentStreak: number;
  streakType: 'win' | 'loss';
}

interface RealTimeMetric {
  label: string;
  value: string;
  change?: string;
  trend: 'up' | 'down' | 'neutral';
  icon: React.ReactNode;
  color: string;
}

export const LiveTradingStats: React.FC = () => {
  const { getTradingStats, getRiskMetrics, getRiskAlerts } = useTrading();
  const { trades, account } = useTradingStore();
  const [stats, setStats] = useState<LiveStats | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [autoRefresh, setAutoRefresh] = useState(true);

  useEffect(() => {
    updateStats();
    
    if (autoRefresh) {
      const interval = setInterval(updateStats, 5000); // Update every 5 seconds
      return () => clearInterval(interval);
    }
  }, [trades, autoRefresh]);

  const updateStats = () => {
    const tradingStats = getTradingStats();
    const riskMetrics = getRiskMetrics();
    
    // Calculate additional metrics
    const completedTrades = trades.filter(t => t.status === 'won' || t.status === 'lost');
    const avgTradeTime = completedTrades.length > 0 
      ? completedTrades.reduce((sum, trade) => {
          const duration = trade.duration || 5;
          return sum + duration;
        }, 0) / completedTrades.length
      : 0;

    // Calculate current streak
    let currentStreak = 0;
    let streakType: 'win' | 'loss' = 'win';
    
    if (completedTrades.length > 0) {
      const sortedTrades = [...completedTrades].sort((a, b) => b.timestamp - a.timestamp);
      const lastTradeResult = sortedTrades[0].status;
      streakType = lastTradeResult === 'won' ? 'win' : 'loss';
      
      for (const trade of sortedTrades) {
        if (trade.status === lastTradeResult) {
          currentStreak++;
        } else {
          break;
        }
      }
    }

    const profitFactor = tradingStats.totalLoss > 0 
      ? tradingStats.totalProfit / tradingStats.totalLoss 
      : tradingStats.totalProfit > 0 ? Infinity : 0;

    setStats({
      ...tradingStats,
      avgTradeTime,
      profitFactor,
      maxDrawdown: riskMetrics.maxDrawdown * 100,
      currentStreak,
      streakType,
    });
    
    setLastUpdate(new Date());
  };

  const getMetrics = (): RealTimeMetric[] => {
    if (!stats || !account) return [];

    const balanceChange = stats.netProfit;
    const balanceChangePercent = account.balance > 0 
      ? (balanceChange / (account.balance - balanceChange)) * 100 
      : 0;

    return [
      {
        label: 'Account Balance',
        value: `$${account.balance.toFixed(2)}`,
        change: `${balanceChange >= 0 ? '+' : ''}$${balanceChange.toFixed(2)}`,
        trend: balanceChange >= 0 ? 'up' : 'down',
        icon: <DollarSign className="w-5 h-5" />,
        color: balanceChange >= 0 ? 'text-green-500' : 'text-red-500',
      },
      {
        label: 'Win Rate',
        value: `${stats.winRate.toFixed(1)}%`,
        change: stats.winRate >= 50 ? 'Good' : 'Poor',
        trend: stats.winRate >= 50 ? 'up' : 'down',
        icon: <Target className="w-5 h-5" />,
        color: stats.winRate >= 50 ? 'text-green-500' : 'text-red-500',
      },
      {
        label: 'Net Profit',
        value: `$${stats.netProfit.toFixed(2)}`,
        change: `${balanceChangePercent >= 0 ? '+' : ''}${balanceChangePercent.toFixed(1)}%`,
        trend: stats.netProfit >= 0 ? 'up' : 'down',
        icon: <TrendingUp className="w-5 h-5" />,
        color: stats.netProfit >= 0 ? 'text-green-500' : 'text-red-500',
      },
      {
        label: 'Profit Factor',
        value: stats.profitFactor === Infinity ? '∞' : stats.profitFactor.toFixed(2),
        change: stats.profitFactor >= 1 ? 'Profitable' : 'Loss',
        trend: stats.profitFactor >= 1 ? 'up' : 'down',
        icon: <BarChart3 className="w-5 h-5" />,
        color: stats.profitFactor >= 1 ? 'text-green-500' : 'text-red-500',
      },
      {
        label: 'Max Drawdown',
        value: `${stats.maxDrawdown.toFixed(1)}%`,
        change: stats.maxDrawdown <= 10 ? 'Low Risk' : 'High Risk',
        trend: stats.maxDrawdown <= 10 ? 'up' : 'down',
        icon: <TrendingDown className="w-5 h-5" />,
        color: stats.maxDrawdown <= 10 ? 'text-green-500' : 'text-red-500',
      },
      {
        label: 'Active Trades',
        value: stats.activeTrades.toString(),
        change: `${stats.totalTrades} Total`,
        trend: 'neutral',
        icon: <Activity className="w-5 h-5" />,
        color: 'text-blue-500',
      },
      {
        label: 'Current Streak',
        value: `${stats.currentStreak} ${stats.streakType}${stats.currentStreak !== 1 ? 's' : ''}`,
        change: stats.streakType === 'win' ? 'Winning' : 'Losing',
        trend: stats.streakType === 'win' ? 'up' : 'down',
        icon: stats.streakType === 'win' ? <Zap className="w-5 h-5" /> : <AlertTriangle className="w-5 h-5" />,
        color: stats.streakType === 'win' ? 'text-green-500' : 'text-red-500',
      },
      {
        label: 'Avg Trade Time',
        value: `${stats.avgTradeTime.toFixed(1)}m`,
        change: 'Duration',
        trend: 'neutral',
        icon: <Clock className="w-5 h-5" />,
        color: 'text-blue-500',
      },
    ];
  };

  const getRiskLevel = (): { level: string; color: string; icon: React.ReactNode } => {
    if (!stats) return { level: 'Unknown', color: 'text-gray-500', icon: <Shield className="w-4 h-4" /> };

    if (stats.maxDrawdown > 20 || stats.winRate < 30) {
      return { level: 'High Risk', color: 'text-red-500', icon: <AlertTriangle className="w-4 h-4" /> };
    } else if (stats.maxDrawdown > 10 || stats.winRate < 50) {
      return { level: 'Medium Risk', color: 'text-yellow-500', icon: <Eye className="w-4 h-4" /> };
    } else {
      return { level: 'Low Risk', color: 'text-green-500', icon: <Shield className="w-4 h-4" /> };
    }
  };

  const metrics = getMetrics();
  const riskLevel = getRiskLevel();
  const riskAlerts = getRiskAlerts();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white">Live Trading Statistics</h2>
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2 text-sm text-gray-400">
            <Clock className="w-4 h-4" />
            <span>Last updated: {lastUpdate.toLocaleTimeString()}</span>
          </div>
          <button
            onClick={() => setAutoRefresh(!autoRefresh)}
            className={`flex items-center px-3 py-1 rounded-lg text-sm transition-colors ${
              autoRefresh 
                ? 'bg-green-600 text-white hover:bg-green-700' 
                : 'bg-gray-600 text-gray-300 hover:bg-gray-700'
            }`}
          >
            <RefreshCw className={`w-4 h-4 mr-1 ${autoRefresh ? 'animate-spin' : ''}`} />
            Auto Refresh
          </button>
          <button
            onClick={updateStats}
            className="flex items-center px-3 py-1 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
          >
            <RefreshCw className="w-4 h-4 mr-1" />
            Refresh
          </button>
        </div>
      </div>

      {/* Risk Level Indicator */}
      <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-lg bg-gray-700 ${riskLevel.color}`}>
              {riskLevel.icon}
            </div>
            <div>
              <h3 className="font-semibold text-white">Risk Assessment</h3>
              <p className={`text-sm ${riskLevel.color}`}>{riskLevel.level}</p>
            </div>
          </div>
          {riskAlerts.length > 0 && (
            <div className="flex items-center space-x-2 text-yellow-500">
              <AlertTriangle className="w-5 h-5" />
              <span className="text-sm font-medium">{riskAlerts.length} Alert{riskAlerts.length !== 1 ? 's' : ''}</span>
            </div>
          )}
        </div>
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {metrics.map((metric, index) => (
          <div key={index} className="bg-gray-800 rounded-lg p-4 border border-gray-700">
            <div className="flex items-center justify-between mb-2">
              <div className={`p-2 rounded-lg bg-gray-700 ${metric.color}`}>
                {metric.icon}
              </div>
              {metric.trend !== 'neutral' && (
                <div className={`flex items-center ${metric.color}`}>
                  {metric.trend === 'up' ? (
                    <TrendingUp className="w-4 h-4" />
                  ) : (
                    <TrendingDown className="w-4 h-4" />
                  )}
                </div>
              )}
            </div>
            <div>
              <p className="text-sm text-gray-400 mb-1">{metric.label}</p>
              <p className={`text-xl font-bold ${metric.color}`}>{metric.value}</p>
              {metric.change && (
                <p className="text-xs text-gray-500 mt-1">{metric.change}</p>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Risk Alerts */}
      {riskAlerts.length > 0 && (
        <div className="bg-gray-800 rounded-lg p-4 border border-red-500">
          <h3 className="text-lg font-semibold text-white mb-3 flex items-center">
            <AlertTriangle className="w-5 h-5 mr-2 text-red-500" />
            Risk Alerts
          </h3>
          <div className="space-y-2">
            {riskAlerts.slice(0, 3).map((alert, index) => (
              <div
                key={index}
                className={`p-3 rounded-lg border-l-4 ${
                  alert.type === 'danger' 
                    ? 'bg-red-900 bg-opacity-20 border-red-500' 
                    : 'bg-yellow-900 bg-opacity-20 border-yellow-500'
                }`}
              >
                <div className="flex items-center justify-between">
                  <p className={`text-sm font-medium ${
                    alert.type === 'danger' ? 'text-red-400' : 'text-yellow-400'
                  }`}>
                    {alert.message}
                  </p>
                  <span className="text-xs text-gray-500">
                    Severity: {alert.severity}/10
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Quick Stats Summary */}
      <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-3">Quick Summary</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <p className="text-2xl font-bold text-green-500">{stats?.wonTrades || 0}</p>
            <p className="text-sm text-gray-400">Wins</p>
          </div>
          <div>
            <p className="text-2xl font-bold text-red-500">{stats?.lostTrades || 0}</p>
            <p className="text-sm text-gray-400">Losses</p>
          </div>
          <div>
            <p className="text-2xl font-bold text-blue-500">{stats?.activeTrades || 0}</p>
            <p className="text-sm text-gray-400">Active</p>
          </div>
          <div>
            <p className="text-2xl font-bold text-white">{stats?.totalTrades || 0}</p>
            <p className="text-sm text-gray-400">Total</p>
          </div>
        </div>
      </div>
    </div>
  );
};
