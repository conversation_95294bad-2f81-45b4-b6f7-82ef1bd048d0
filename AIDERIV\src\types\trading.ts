export interface MarketData {
  id: string;
  symbol: string;
  name: string;
  lastPrice: number;
  change: number;
  changePercent: number;
  timestamp: number;
  digits: number[];
  volume: number;
}

export interface ChartData {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export interface TickData {
  symbol: string;
  quote: number;
  epoch: number;
}

export interface OHLCData {
  open: string;
  high: string;
  low: string;
  close: string;
  epoch: number;
  volume?: string;
}

export interface Trade {
  id: string;
  timestamp: number;
  symbol: string;
  market: string;
  type: TradingMode;
  direction: TradeDirection;
  stake: number;
  duration: number;
  prediction?: number;
  entryPrice?: number;
  exitPrice?: number;
  payout?: number;
  profit?: number;
  status: TradeStatus;
  confidence?: number;
  isSold?: boolean;
  transactionId?: string;
}

export interface AccountInfo {
  id: string;
  currency: string;
  balance: number;
  name?: string;
  email?: string;
  isVirtual?: boolean;
  totalProfit?: number;
  totalLoss?: number;
  winRate?: number;
  tradeCount?: number;
}

export interface BalanceResponse {
  balance: AccountInfo;
  [key: string]: any;
}

export interface TradingSettings {
  stake: number;
  duration: number;
  targetProfit: number;
  stopLoss: number;
  prediction: number;
  autoTrading: boolean;
  riskLevel: 'low' | 'medium' | 'high';
}

export interface AISignal {
  id: string;
  timestamp: number;
  symbol: string;
  direction: TradeDirection;
  confidence: number;
  reason: string;
  type: TradingMode;
  prediction?: number;
  validity: number; // seconds
}

export type TradingMode = 'ai' | 'over_under' | 'diff_match' | 'rise_fall' | 'quadrant';
export type TradeDirection = 'up' | 'down' | 'over' | 'under' | 'diff' | 'match' | 'rise' | 'fall' | 'buy' | 'sell';
export enum TradeStatus {
  Pending = 'pending',
  Active = 'active',
  Won = 'won',
  Lost = 'lost',
  Cancelled = 'cancelled',
}
export type MarketSymbol = 'R_100' | 'R_10' | 'R_25' | 'R_50' | 'R_75' | '1HZ10V' | '1HZ25V' | '1HZ50V' | '1HZ75V' | '1HZ100V';

export interface EventHandlers {
  onConnectionStatusChange?: (connected: boolean, message?: string) => void;
  onMarketDataUpdate?: (data: MarketData) => void;
  onChartDataUpdate?: (symbol: string, data: ChartData[]) => void;
  onAccountUpdate?: (account: AccountInfo) => void;
  onTradeUpdate?: (trade: Trade) => void;
  onTransactionUpdate?: (transaction: any) => void;
}

export interface MarketCondition {
  symbol: string;
  trend: 'bullish' | 'bearish' | 'sideways';
  volatility: 'low' | 'medium' | 'high';
  support: number;
  resistance: number;
  momentum: number;
}

export interface NotificationConfig {
  sound: boolean;
  desktop: boolean;
  email: boolean;
  trades: boolean;
  signals: boolean;
  profits: boolean;
}

export interface ThemeConfig {
  mode: 'light' | 'dark';
  primaryColor: string;
  accentColor: string;
  fontSize: 'small' | 'medium' | 'large';
}

export interface ProposalOpenContract {
  [key: string]: any;
  contract_id: number;
  display_name: string;
  contract_type: string;
  entry_tick: string;
  exit_tick: string;
  stake: number;
  profit: number;
  is_sold: number;
  entry_tick_time: number;
  exit_tick_time: number;
}

export interface ProposalOpenContractResponse {
  proposal_open_contract: ProposalOpenContract;
  [key: string]: any;
}

export interface Transaction {
  [key:string]: any;
  action: 'buy' | 'sell';
  amount: number;
  balance_after: number;
  contract_id: number;
  longcode: string;
  transaction_id: number;
  transaction_time: number;
}

export interface TransactionResponse {
  transaction: Transaction;
  [key: string]: any;
}

// Quadrant Strategy Types
export type QuadrantNumber = 1 | 2 | 3 | 4;
export type DigitValue = 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9;
export type BlueRedScale = 'b' | 'r';
export type RankLevel = 'highest' | '2nd_highest' | 'middle' | '2nd_lowest' | 'lowest';

export interface QuadrantPosition {
  quadrant: QuadrantNumber;
  rank: RankLevel;
  value: DigitValue;
  scale: BlueRedScale;
  scaleValue: number; // 0-10 on b/r scale
}

export interface DigitMovement {
  from: QuadrantPosition;
  to: QuadrantPosition;
  direction: 'up' | 'down';
  isLegal: boolean;
  isClockwise: boolean;
}

export interface QuadrantSequence {
  digits: DigitValue[];
  movements: DigitMovement[];
  trendBias: 'up' | 'down';
  isConsistent: boolean;
  hasContradiction: boolean;
  resolvedPath?: DigitMovement[];
  finalDirection?: 'rise' | 'fall';
}

export interface QuadrantAnalysis {
  sequence: QuadrantSequence;
  confidence: number;
  reason: string;
  interchangesUsed: Array<{
    original: DigitValue;
    replacement: DigitValue;
    reason: string;
  }>;
  pathTrace: string[];
  recommendation: {
    action: 'rise' | 'fall';
    confidence: number;
    reasoning: string;
  };
}

export interface QuadrantChart {
  Q1: { [key in RankLevel]: DigitValue };
  Q2: { [key in RankLevel]: DigitValue };
  Q3: { [key in RankLevel]: DigitValue };
  Q4: { [key in RankLevel]: DigitValue };
}

export interface MovementScale {
  blue: { [key: number]: number }; // b scale: 0b-10b
  red: { [key: number]: number };  // r scale: 0r-10r
}
