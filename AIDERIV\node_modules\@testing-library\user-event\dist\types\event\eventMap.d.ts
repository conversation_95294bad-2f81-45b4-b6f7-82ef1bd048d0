import { EventType } from './types';
export declare const eventMap: {
    readonly auxclick: {
        readonly EventType: "PointerEvent";
        readonly defaultInit: {
            readonly bubbles: true;
            readonly cancelable: true;
            readonly composed: true;
        };
    };
    readonly beforeinput: {
        readonly EventType: "InputEvent";
        readonly defaultInit: {
            readonly bubbles: true;
            readonly cancelable: true;
            readonly composed: true;
        };
    };
    readonly blur: {
        readonly EventType: "FocusEvent";
        readonly defaultInit: {
            readonly bubbles: false;
            readonly cancelable: false;
            readonly composed: true;
        };
    };
    readonly click: {
        readonly EventType: "PointerEvent";
        readonly defaultInit: {
            readonly bubbles: true;
            readonly cancelable: true;
            readonly composed: true;
        };
    };
    readonly contextmenu: {
        readonly EventType: "PointerEvent";
        readonly defaultInit: {
            readonly bubbles: true;
            readonly cancelable: true;
            readonly composed: true;
        };
    };
    readonly copy: {
        readonly EventType: "ClipboardEvent";
        readonly defaultInit: {
            readonly bubbles: true;
            readonly cancelable: true;
            readonly composed: true;
        };
    };
    readonly change: {
        readonly EventType: "Event";
        readonly defaultInit: {
            readonly bubbles: true;
            readonly cancelable: false;
        };
    };
    readonly cut: {
        readonly EventType: "ClipboardEvent";
        readonly defaultInit: {
            readonly bubbles: true;
            readonly cancelable: true;
            readonly composed: true;
        };
    };
    readonly dblclick: {
        readonly EventType: "MouseEvent";
        readonly defaultInit: {
            readonly bubbles: true;
            readonly cancelable: true;
            readonly composed: true;
        };
    };
    readonly focus: {
        readonly EventType: "FocusEvent";
        readonly defaultInit: {
            readonly bubbles: false;
            readonly cancelable: false;
            readonly composed: true;
        };
    };
    readonly focusin: {
        readonly EventType: "FocusEvent";
        readonly defaultInit: {
            readonly bubbles: true;
            readonly cancelable: false;
            readonly composed: true;
        };
    };
    readonly focusout: {
        readonly EventType: "FocusEvent";
        readonly defaultInit: {
            readonly bubbles: true;
            readonly cancelable: false;
            readonly composed: true;
        };
    };
    readonly keydown: {
        readonly EventType: "KeyboardEvent";
        readonly defaultInit: {
            readonly bubbles: true;
            readonly cancelable: true;
            readonly composed: true;
        };
    };
    readonly keypress: {
        readonly EventType: "KeyboardEvent";
        readonly defaultInit: {
            readonly bubbles: true;
            readonly cancelable: true;
            readonly composed: true;
        };
    };
    readonly keyup: {
        readonly EventType: "KeyboardEvent";
        readonly defaultInit: {
            readonly bubbles: true;
            readonly cancelable: true;
            readonly composed: true;
        };
    };
    readonly paste: {
        readonly EventType: "ClipboardEvent";
        readonly defaultInit: {
            readonly bubbles: true;
            readonly cancelable: true;
            readonly composed: true;
        };
    };
    readonly input: {
        readonly EventType: "InputEvent";
        readonly defaultInit: {
            readonly bubbles: true;
            readonly cancelable: false;
            readonly composed: true;
        };
    };
    readonly mousedown: {
        readonly EventType: "MouseEvent";
        readonly defaultInit: {
            readonly bubbles: true;
            readonly cancelable: true;
            readonly composed: true;
        };
    };
    readonly mouseenter: {
        readonly EventType: "MouseEvent";
        readonly defaultInit: {
            readonly bubbles: false;
            readonly cancelable: false;
            readonly composed: true;
        };
    };
    readonly mouseleave: {
        readonly EventType: "MouseEvent";
        readonly defaultInit: {
            readonly bubbles: false;
            readonly cancelable: false;
            readonly composed: true;
        };
    };
    readonly mousemove: {
        readonly EventType: "MouseEvent";
        readonly defaultInit: {
            readonly bubbles: true;
            readonly cancelable: true;
            readonly composed: true;
        };
    };
    readonly mouseout: {
        readonly EventType: "MouseEvent";
        readonly defaultInit: {
            readonly bubbles: true;
            readonly cancelable: true;
            readonly composed: true;
        };
    };
    readonly mouseover: {
        readonly EventType: "MouseEvent";
        readonly defaultInit: {
            readonly bubbles: true;
            readonly cancelable: true;
            readonly composed: true;
        };
    };
    readonly mouseup: {
        readonly EventType: "MouseEvent";
        readonly defaultInit: {
            readonly bubbles: true;
            readonly cancelable: true;
            readonly composed: true;
        };
    };
    readonly pointerover: {
        readonly EventType: "PointerEvent";
        readonly defaultInit: {
            readonly bubbles: true;
            readonly cancelable: true;
            readonly composed: true;
        };
    };
    readonly pointerenter: {
        readonly EventType: "PointerEvent";
        readonly defaultInit: {
            readonly bubbles: false;
            readonly cancelable: false;
        };
    };
    readonly pointerdown: {
        readonly EventType: "PointerEvent";
        readonly defaultInit: {
            readonly bubbles: true;
            readonly cancelable: true;
            readonly composed: true;
        };
    };
    readonly pointermove: {
        readonly EventType: "PointerEvent";
        readonly defaultInit: {
            readonly bubbles: true;
            readonly cancelable: true;
            readonly composed: true;
        };
    };
    readonly pointerup: {
        readonly EventType: "PointerEvent";
        readonly defaultInit: {
            readonly bubbles: true;
            readonly cancelable: true;
            readonly composed: true;
        };
    };
    readonly pointercancel: {
        readonly EventType: "PointerEvent";
        readonly defaultInit: {
            readonly bubbles: true;
            readonly cancelable: false;
            readonly composed: true;
        };
    };
    readonly pointerout: {
        readonly EventType: "PointerEvent";
        readonly defaultInit: {
            readonly bubbles: true;
            readonly cancelable: true;
            readonly composed: true;
        };
    };
    readonly pointerleave: {
        readonly EventType: "PointerEvent";
        readonly defaultInit: {
            readonly bubbles: false;
            readonly cancelable: false;
        };
    };
    readonly submit: {
        readonly EventType: "Event";
        readonly defaultInit: {
            readonly bubbles: true;
            readonly cancelable: true;
        };
    };
};
export declare function isMouseEvent(type: EventType): boolean;
export declare function isKeyboardEvent(type: EventType): boolean;
