import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { Button } from './ui/button';
import { 
  QuadrantAnalysis as QuadrantAnalysisType, 
  DigitValue, 
  QuadrantPosition,
  DigitMovement 
} from '../types/trading';
import { AITradingService } from '../services/aiService';
import { useTradingStore } from '../store/tradingStore';

interface QuadrantAnalysisProps {
  aiService: AITradingService;
  className?: string;
}

export const QuadrantAnalysis: React.FC<QuadrantAnalysisProps> = ({ 
  aiService, 
  className = '' 
}) => {
  const { currentMarket, marketData } = useTradingStore();
  const [analysis, setAnalysis] = useState<QuadrantAnalysisType | null>(null);
  const [currentDigits, setCurrentDigits] = useState<DigitValue[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  useEffect(() => {
    if (marketData[currentMarket]) {
      const digits = aiService.getCurrentDigits(marketData[currentMarket]);
      setCurrentDigits(digits);
      
      if (digits.length >= 3) {
        const quadrantAnalysis = aiService.getQuadrantAnalysis(currentMarket, marketData[currentMarket]);
        setAnalysis(quadrantAnalysis);
      }
    }
  }, [marketData, currentMarket, aiService]);

  const analyzeSequence = () => {
    if (currentDigits.length < 3) return;
    
    setIsAnalyzing(true);
    try {
      const quadrantAnalysis = aiService.getQuadrantAnalysis(currentMarket, marketData[currentMarket]);
      setAnalysis(quadrantAnalysis);
    } catch (error) {
      console.error('Analysis failed:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const getQuadrantColor = (quadrant: number): string => {
    switch (quadrant) {
      case 1:
      case 2:
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 3:
      case 4:
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getDirectionColor = (direction: string): string => {
    return direction === 'up' || direction === 'rise' 
      ? 'text-green-600' 
      : 'text-red-600';
  };

  const renderQuadrantChart = () => (
    <div className="grid grid-cols-2 gap-3 p-4 bg-gray-50 rounded-lg">
      <div className="text-center bg-blue-50 p-3 rounded-lg border-2 border-blue-200">
        <div className="font-semibold text-blue-700 mb-2">Q1 (Blue Zone)</div>
        <div className="space-y-1 text-sm">
          <div className="bg-blue-100 px-2 py-1 rounded">Highest: <span className="font-mono font-bold">5</span></div>
          <div className="bg-blue-100 px-2 py-1 rounded">2nd High: <span className="font-mono font-bold">4</span></div>
          <div className="bg-blue-100 px-2 py-1 rounded">Middle: <span className="font-mono font-bold">3</span></div>
          <div className="bg-blue-100 px-2 py-1 rounded">2nd Low: <span className="font-mono font-bold">2</span></div>
          <div className="bg-blue-100 px-2 py-1 rounded">Lowest: <span className="font-mono font-bold">1</span></div>
        </div>
      </div>
      <div className="text-center bg-blue-50 p-3 rounded-lg border-2 border-blue-200">
        <div className="font-semibold text-blue-700 mb-2">Q2 (Blue Zone)</div>
        <div className="space-y-1 text-sm">
          <div className="bg-blue-100 px-2 py-1 rounded">Highest: <span className="font-mono font-bold">9</span></div>
          <div className="bg-blue-100 px-2 py-1 rounded">2nd High: <span className="font-mono font-bold">8</span></div>
          <div className="bg-blue-100 px-2 py-1 rounded">Middle: <span className="font-mono font-bold">7</span></div>
          <div className="bg-blue-100 px-2 py-1 rounded">2nd Low: <span className="font-mono font-bold">6</span></div>
          <div className="bg-blue-100 px-2 py-1 rounded">Lowest: <span className="font-mono font-bold">5</span></div>
        </div>
      </div>
      <div className="text-center bg-red-50 p-3 rounded-lg border-2 border-red-200">
        <div className="font-semibold text-red-700 mb-2">Q3 (Red Zone)</div>
        <div className="space-y-1 text-sm">
          <div className="bg-red-100 px-2 py-1 rounded">Highest: <span className="font-mono font-bold">1</span></div>
          <div className="bg-red-100 px-2 py-1 rounded">2nd High: <span className="font-mono font-bold">2</span></div>
          <div className="bg-red-100 px-2 py-1 rounded">Middle: <span className="font-mono font-bold">3</span></div>
          <div className="bg-red-100 px-2 py-1 rounded">2nd Low: <span className="font-mono font-bold">4</span></div>
          <div className="bg-red-100 px-2 py-1 rounded">Lowest: <span className="font-mono font-bold">5</span></div>
        </div>
      </div>
      <div className="text-center bg-red-50 p-3 rounded-lg border-2 border-red-200">
        <div className="font-semibold text-red-700 mb-2">Q4 (Red Zone)</div>
        <div className="space-y-1 text-sm">
          <div className="bg-red-100 px-2 py-1 rounded">Highest: <span className="font-mono font-bold">5</span></div>
          <div className="bg-red-100 px-2 py-1 rounded">2nd High: <span className="font-mono font-bold">6</span></div>
          <div className="bg-red-100 px-2 py-1 rounded">Middle: <span className="font-mono font-bold">7</span></div>
          <div className="bg-red-100 px-2 py-1 rounded">2nd Low: <span className="font-mono font-bold">8</span></div>
          <div className="bg-red-100 px-2 py-1 rounded">Lowest: <span className="font-mono font-bold">9</span></div>
        </div>
      </div>
    </div>
  );

  const renderDigitSequence = () => (
    <div className="space-y-2">
      <div className="flex items-center gap-2">
        <span className="font-medium">Current Digits:</span>
        <div className="flex gap-1">
          {currentDigits.map((digit, index) => (
            <Badge 
              key={index} 
              variant="outline" 
              className="text-lg font-mono px-3 py-1"
            >
              {digit}
            </Badge>
          ))}
        </div>
      </div>
      
      {analysis && (
        <div className="flex items-center gap-2">
          <span className="font-medium">Sequence:</span>
          <div className="flex gap-1">
            {analysis.sequence.digits.map((digit, index) => (
              <Badge 
                key={index} 
                variant="outline" 
                className="text-lg font-mono px-3 py-1"
              >
                {digit}
              </Badge>
            ))}
          </div>
        </div>
      )}
    </div>
  );

  const renderMovementPath = () => {
    if (!analysis || !analysis.pathTrace.length) return null;

    return (
      <div className="space-y-2">
        <div className="font-medium">Movement Path:</div>
        <div className="flex items-center gap-2 flex-wrap">
          {analysis.pathTrace.map((step, index) => (
            <React.Fragment key={index}>
              <Badge 
                variant="outline" 
                className="font-mono px-2 py-1"
              >
                {step}
              </Badge>
              {index < analysis.pathTrace.length - 1 && (
                <span className="text-gray-400">→</span>
              )}
            </React.Fragment>
          ))}
        </div>
      </div>
    );
  };

  const renderAnalysisResults = () => {
    if (!analysis) return null;

    return (
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <div className="font-medium mb-1">Trend Bias:</div>
            <Badge 
              className={`${getDirectionColor(analysis.sequence.trendBias)} bg-transparent border`}
            >
              {analysis.sequence.trendBias.toUpperCase()}
            </Badge>
          </div>
          <div>
            <div className="font-medium mb-1">Final Direction:</div>
            <Badge 
              className={`${getDirectionColor(analysis.recommendation.action)} bg-transparent border`}
            >
              {analysis.recommendation.action.toUpperCase()}
            </Badge>
          </div>
        </div>

        <div>
          <div className="font-medium mb-1">Confidence:</div>
          <div className="flex items-center gap-2">
            <div className="flex-1 bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${analysis.confidence * 100}%` }}
              />
            </div>
            <span className="text-sm font-mono">
              {(analysis.confidence * 100).toFixed(1)}%
            </span>
          </div>
        </div>

        <div>
          <div className="font-medium mb-1">Analysis:</div>
          <p className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
            {analysis.reason}
          </p>
        </div>

        {analysis.interchangesUsed.length > 0 && (
          <div>
            <div className="font-medium mb-1">Interchanges Used:</div>
            <div className="space-y-1">
              {analysis.interchangesUsed.map((interchange, index) => (
                <div key={index} className="text-sm bg-yellow-50 p-2 rounded border-l-4 border-yellow-400">
                  <span className="font-mono">{interchange.original}</span>
                  <span className="mx-2">→</span>
                  <span className="font-mono">{interchange.replacement}</span>
                  <span className="text-gray-600 ml-2">({interchange.reason})</span>
                </div>
              ))}
            </div>
          </div>
        )}

        <div>
          <div className="font-medium mb-1">Recommendation:</div>
          <div className="bg-blue-50 p-3 rounded border-l-4 border-blue-400">
            <div className="flex items-center gap-2 mb-1">
              <Badge 
                className={`${getDirectionColor(analysis.recommendation.action)} bg-transparent border`}
              >
                {analysis.recommendation.action.toUpperCase()}
              </Badge>
              <span className="text-sm">
                ({(analysis.recommendation.confidence * 100).toFixed(1)}% confidence)
              </span>
            </div>
            <p className="text-sm text-gray-700">
              {analysis.recommendation.reasoning}
            </p>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Quadrant Strategy Analysis
            <Button 
              onClick={analyzeSequence}
              disabled={isAnalyzing || currentDigits.length < 3}
              size="sm"
            >
              {isAnalyzing ? 'Analyzing...' : 'Analyze'}
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {renderDigitSequence()}
          {renderMovementPath()}
          {renderAnalysisResults()}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Quadrant Chart Reference</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {renderQuadrantChart()}

          {/* Color Legend */}
          <div className="bg-gray-100 p-3 rounded-lg">
            <div className="text-sm font-semibold mb-2">Color Legend:</div>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-blue-100 border border-blue-300 rounded"></div>
                <span>Blue Zone (Q1, Q2) - UP Trend</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-red-100 border border-red-300 rounded"></div>
                <span>Red Zone (Q3, Q4) - DOWN Trend</span>
              </div>
            </div>
            <div className="mt-2 text-xs text-gray-600">
              Movement follows clockwise path: Q1 → Q2 → Q3 → Q4 → Q1
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
