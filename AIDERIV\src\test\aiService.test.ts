import { describe, it, expect, beforeEach, vi } from 'vitest';
import { getAIService } from '../services/aiService';
import { MarketData, ChartData } from '../types/trading';

describe('AITradingService', () => {
  let aiService: ReturnType<typeof getAIService>;
  let mockMarketData: MarketData;
  let mockChartData: ChartData[];

  beforeEach(() => {
    aiService = getAIService();
    
    mockMarketData = {
      symbol: 'R_100',
      lastPrice: 1000.50,
      change: 0.25,
      changePercent: 0.025,
      digits: [1, 0, 0, 0, 5, 0],
      timestamp: Date.now(),
    };

    mockChartData = Array.from({ length: 50 }, (_, i) => ({
      timestamp: Date.now() - (50 - i) * 60000,
      open: 1000 + Math.random() * 10,
      high: 1005 + Math.random() * 10,
      low: 995 + Math.random() * 10,
      close: 1000 + Math.random() * 10,
      volume: 1000 + Math.random() * 500,
    }));
  });

  describe('analyzeMarket', () => {
    it('should return null for insufficient data', () => {
      const shortChartData = mockChartData.slice(0, 10);
      const result = aiService.analyzeMarket('R_100', mockMarketData, shortChartData);
      expect(result).toBeNull();
    });

    it('should return AI signal with sufficient data', () => {
      const result = aiService.analyzeMarket('R_100', mockMarketData, mockChartData);
      
      if (result) {
        expect(result).toHaveProperty('id');
        expect(result).toHaveProperty('timestamp');
        expect(result).toHaveProperty('symbol', 'R_100');
        expect(result).toHaveProperty('direction');
        expect(result).toHaveProperty('confidence');
        expect(result).toHaveProperty('reason');
        expect(result).toHaveProperty('type', 'ai');
        expect(result.confidence).toBeGreaterThanOrEqual(0.6);
        expect(['up', 'down']).toContain(result.direction);
      }
    });

    it('should generate consistent signals for same data', () => {
      const result1 = aiService.analyzeMarket('R_100', mockMarketData, mockChartData);
      const result2 = aiService.analyzeMarket('R_100', mockMarketData, mockChartData);
      
      if (result1 && result2) {
        expect(result1.direction).toBe(result2.direction);
        expect(Math.abs(result1.confidence - result2.confidence)).toBeLessThan(0.1);
      }
    });
  });

  describe('getMarketCondition', () => {
    it('should return null for insufficient data', () => {
      const shortChartData = mockChartData.slice(0, 10);
      const result = aiService.getMarketCondition('R_100', shortChartData);
      expect(result).toBeNull();
    });

    it('should return market condition with sufficient data', () => {
      const result = aiService.getMarketCondition('R_100', mockChartData);
      
      expect(result).toBeTruthy();
      if (result) {
        expect(result).toHaveProperty('symbol', 'R_100');
        expect(result).toHaveProperty('trend');
        expect(result).toHaveProperty('volatility');
        expect(result).toHaveProperty('support');
        expect(result).toHaveProperty('resistance');
        expect(result).toHaveProperty('momentum');
        expect(['bullish', 'bearish', 'sideways']).toContain(result.trend);
        expect(['low', 'medium', 'high']).toContain(result.volatility);
      }
    });
  });

  describe('Technical Indicators', () => {
    it('should calculate RSI correctly', () => {
      // Test with known data
      const prices = [44, 44.34, 44.09, 44.15, 43.61, 44.33, 44.83, 45.85, 46.08, 45.89, 46.03, 46.83, 46.69, 46.45, 46.59];
      
      // Access private method through any casting for testing
      const rsi = (aiService as any).calculateRSI(prices, 14);
      
      expect(rsi).toBeGreaterThan(0);
      expect(rsi).toBeLessThan(100);
      expect(typeof rsi).toBe('number');
    });

    it('should calculate SMA correctly', () => {
      const prices = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
      const sma = (aiService as any).calculateSMA(prices, 5);
      
      expect(sma).toHaveLength(6); // 10 - 5 + 1
      expect(sma[0]).toBe(3); // (1+2+3+4+5)/5
      expect(sma[sma.length - 1]).toBe(8); // (6+7+8+9+10)/5
    });

    it('should calculate EMA correctly', () => {
      const prices = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
      const ema = (aiService as any).calculateEMA(prices, 5);
      
      expect(ema).toHaveLength(10);
      expect(ema[0]).toBe(1); // First value
      expect(ema[ema.length - 1]).toBeGreaterThan(ema[0]);
    });

    it('should calculate Bollinger Bands correctly', () => {
      const prices = Array.from({ length: 25 }, (_, i) => 100 + Math.sin(i) * 5);
      const bollinger = (aiService as any).calculateBollingerBands(prices, 20, 2);
      
      expect(bollinger).toHaveProperty('upper');
      expect(bollinger).toHaveProperty('middle');
      expect(bollinger).toHaveProperty('lower');
      expect(bollinger.upper).toBeGreaterThan(bollinger.middle);
      expect(bollinger.middle).toBeGreaterThan(bollinger.lower);
    });
  });

  describe('Pattern Recognition', () => {
    it('should detect hammer pattern', () => {
      const hammerCandle: ChartData = {
        timestamp: Date.now(),
        open: 100,
        high: 102,
        low: 95,
        close: 101,
        volume: 1000,
      };
      
      const isHammer = (aiService as any).isHammer(hammerCandle);
      expect(typeof isHammer).toBe('boolean');
    });

    it('should detect shooting star pattern', () => {
      const shootingStarCandle: ChartData = {
        timestamp: Date.now(),
        open: 100,
        high: 105,
        low: 99,
        close: 99.5,
        volume: 1000,
      };
      
      const isShootingStar = (aiService as any).isShootingStar(shootingStarCandle);
      expect(typeof isShootingStar).toBe('boolean');
    });

    it('should detect bullish engulfing pattern', () => {
      const prevCandle: ChartData = {
        timestamp: Date.now() - 60000,
        open: 102,
        high: 103,
        low: 100,
        close: 101,
        volume: 1000,
      };
      
      const currentCandle: ChartData = {
        timestamp: Date.now(),
        open: 100,
        high: 105,
        low: 99,
        close: 104,
        volume: 1000,
      };
      
      const isBullishEngulfing = (aiService as any).isBullishEngulfing(prevCandle, currentCandle);
      expect(typeof isBullishEngulfing).toBe('boolean');
    });
  });

  describe('Advanced Indicators', () => {
    it('should calculate Williams %R correctly', () => {
      const highs = [110, 112, 108, 115, 120];
      const lows = [100, 105, 102, 108, 110];
      const closes = [105, 110, 106, 112, 118];
      
      const williamsR = (aiService as any).calculateWilliamsR(highs, lows, closes, 5);
      
      expect(williamsR).toBeGreaterThanOrEqual(-100);
      expect(williamsR).toBeLessThanOrEqual(0);
    });

    it('should calculate CCI correctly', () => {
      const highs = [110, 112, 108, 115, 120];
      const lows = [100, 105, 102, 108, 110];
      const closes = [105, 110, 106, 112, 118];
      
      const cci = (aiService as any).calculateCCI(highs, lows, closes, 5);
      
      expect(typeof cci).toBe('number');
      expect(isFinite(cci)).toBe(true);
    });

    it('should calculate ATR correctly', () => {
      const highs = [110, 112, 108, 115, 120];
      const lows = [100, 105, 102, 108, 110];
      const closes = [105, 110, 106, 112, 118];
      
      const atr = (aiService as any).calculateATR(highs, lows, closes, 4);
      
      expect(atr).toBeGreaterThanOrEqual(0);
      expect(typeof atr).toBe('number');
    });

    it('should calculate Ichimoku correctly', () => {
      const highs = Array.from({ length: 60 }, (_, i) => 100 + Math.random() * 10);
      const lows = Array.from({ length: 60 }, (_, i) => 90 + Math.random() * 10);
      const closes = Array.from({ length: 60 }, (_, i) => 95 + Math.random() * 10);
      
      const ichimoku = (aiService as any).calculateIchimoku(highs, lows, closes);
      
      expect(ichimoku).toHaveProperty('tenkanSen');
      expect(ichimoku).toHaveProperty('kijunSen');
      expect(ichimoku).toHaveProperty('senkouSpanA');
      expect(ichimoku).toHaveProperty('senkouSpanB');
      expect(ichimoku).toHaveProperty('chikouSpan');
      
      Object.values(ichimoku).forEach(value => {
        expect(typeof value).toBe('number');
        expect(isFinite(value)).toBe(true);
      });
    });
  });

  describe('Performance', () => {
    it('should analyze market within reasonable time', () => {
      const startTime = performance.now();
      
      for (let i = 0; i < 10; i++) {
        aiService.analyzeMarket('R_100', mockMarketData, mockChartData);
      }
      
      const endTime = performance.now();
      const avgTime = (endTime - startTime) / 10;
      
      expect(avgTime).toBeLessThan(100); // Should complete within 100ms on average
    });
  });
});
