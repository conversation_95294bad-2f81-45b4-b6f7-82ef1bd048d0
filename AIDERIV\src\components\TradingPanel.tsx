import React, { useState } from 'react';
import {
  Brain,
  ArrowUp,
  ArrowDown,
  Target,
  TrendingUp,
  TrendingDown,
  Play,
  Square,
  Settings,
  Zap,
  Grid3X3,
} from 'lucide-react';
import { TradingMode, TradeDirection } from '../types/trading';
import { useTradingStore, useCurrentSettings } from '../store/tradingStore';
import { useTrading } from '../hooks/useTrading';
import { QuadrantWidget } from './QuadrantWidget';
import { getAIService } from '../services/aiService';
import toast from 'react-hot-toast';

interface TradingPanelProps {
  className?: string;
}

const TradingPanel: React.FC<TradingPanelProps> = ({ className = '' }) => {
  const { currentMode, isConnected } = useTradingStore();
  const settings = useCurrentSettings();
  const {
    changeMode,
    updateTradingSettings,
    placeBinaryTrade,
    placeDigitTrade,
    startAutoTrading,
    stopAutoTrading,
    executeTrade,
  } = useTrading();

  const [prediction, setPrediction] = useState(5);
  const [showSettings, setShowSettings] = useState(false);

  const tradingModes = [
    { id: 'ai', label: 'AI ✨', icon: Brain, color: 'from-purple-500 to-indigo-600' },
    { id: 'quadrant', label: 'Quadrant', icon: Grid3X3, color: 'from-violet-500 to-purple-600' },
    { id: 'over_under', label: 'Over | Under', icon: Target, color: 'from-blue-500 to-cyan-600' },
    { id: 'diff_match', label: 'Diff | Match', icon: Zap, color: 'from-green-500 to-teal-600' },
    { id: 'rise_fall', label: 'Rise | Fall', icon: TrendingUp, color: 'from-orange-500 to-red-600' },
  ];

  const handleModeChange = (mode: TradingMode) => {
    changeMode(mode);
    toast.success(`Switched to ${tradingModes.find(m => m.id === mode)?.label} mode`);
  };

  const handleSettingChange = (key: string, value: any) => {
    updateTradingSettings(currentMode, { [key]: value });
  };

  const handleAutoTradingToggle = () => {
    if (settings.autoTrading) {
      stopAutoTrading();
      handleSettingChange('autoTrading', false);
    } else {
      startAutoTrading();
      handleSettingChange('autoTrading', true);
    }
  };

  const renderModeSelector = () => (
    <div className="grid grid-cols-2 lg:grid-cols-4 gap-2">
      {tradingModes.map((mode) => {
        const Icon = mode.icon;
        const isActive = currentMode === mode.id;
        
        return (
          <button
            key={mode.id}
            onClick={() => handleModeChange(mode.id as TradingMode)}
            className={`relative p-3 rounded-lg transition-all duration-200 ${
              isActive
                ? `bg-gradient-to-r ${mode.color} text-white shadow-lg scale-105`
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600 hover:text-white'
            }`}
          >
            <div className="flex items-center justify-center space-x-2">
              <Icon className="w-4 h-4" />
              <span className="text-sm font-medium">{mode.label}</span>
            </div>
            {isActive && (
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-gray-800"></div>
            )}
          </button>
        );
      })}
    </div>
  );

  const renderAIMode = () => (
    <div className="space-y-4">
      <div className="bg-gradient-to-r from-purple-600/20 to-indigo-600/20 rounded-lg p-4 border border-purple-500/30">
        <div className="flex items-center space-x-2 mb-3">
          <Brain className="w-5 h-5 text-purple-400" />
          <h3 className="font-semibold text-white">AI Trading Mode</h3>
        </div>
        <p className="text-sm text-gray-300 mb-4">
          Automated trading using advanced AI algorithms and technical analysis.
        </p>
        
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm text-gray-400 mb-1">Stake</label>
            <input
              type="number"
              value={settings.stake}
              onChange={(e) => handleSettingChange('stake', parseFloat(e.target.value))}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              min="0.1"
              step="0.1"
            />
          </div>
          <div>
            <label className="block text-sm text-gray-400 mb-1">Target Profit</label>
            <input
              type="number"
              value={settings.targetProfit}
              onChange={(e) => handleSettingChange('targetProfit', parseFloat(e.target.value))}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              min="1"
            />
          </div>
          <div>
            <label className="block text-sm text-gray-400 mb-1">Stop Loss</label>
            <input
              type="number"
              value={settings.stopLoss}
              onChange={(e) => handleSettingChange('stopLoss', parseFloat(e.target.value))}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              min="1"
            />
          </div>
          <div>
            <label className="block text-sm text-gray-400 mb-1">Risk Level</label>
            <select
              value={settings.riskLevel}
              onChange={(e) => handleSettingChange('riskLevel', e.target.value)}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
            >
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
            </select>
          </div>
        </div>

        <button
          onClick={handleAutoTradingToggle}
          disabled={!isConnected}
          className={`w-full mt-4 py-3 rounded-lg font-semibold transition-all ${
            settings.autoTrading
              ? 'bg-red-600 hover:bg-red-700 text-white'
              : 'bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white'
          } disabled:opacity-50 disabled:cursor-not-allowed`}
        >
          {settings.autoTrading ? (
            <span className="flex items-center justify-center space-x-2">
              <Square className="w-4 h-4" />
              <span>Stop AI Trading</span>
            </span>
          ) : (
            <span className="flex items-center justify-center space-x-2">
              <Play className="w-4 h-4" />
              <span>Start AI Trading</span>
            </span>
          )}
        </button>
      </div>
    </div>
  );

  const renderOverUnderMode = () => (
    <div className="space-y-4">
      <div className="bg-gradient-to-r from-blue-600/20 to-cyan-600/20 rounded-lg p-4 border border-blue-500/30">
        <div className="flex items-center space-x-2 mb-3">
          <Target className="w-5 h-5 text-blue-400" />
          <h3 className="font-semibold text-white">Over | Under Mode</h3>
        </div>
        
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm text-gray-400 mb-1">Stake</label>
            <input
              type="number"
              value={settings.stake}
              onChange={(e) => handleSettingChange('stake', parseFloat(e.target.value))}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              min="0.1"
              step="0.1"
            />
          </div>
          <div>
            <label className="block text-sm text-gray-400 mb-1">Duration (ticks)</label>
            <input
              type="number"
              value={settings.duration}
              onChange={(e) => handleSettingChange('duration', parseInt(e.target.value))}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              min="1"
              max="10"
            />
          </div>
        </div>

        <div className="mb-4">
          <label className="block text-sm text-gray-400 mb-2">Prediction Digit</label>
          <div className="flex space-x-1">
            {Array.from({ length: 10 }, (_, i) => (
              <button
                key={i}
                onClick={() => setPrediction(i)}
                className={`w-8 h-8 rounded text-sm font-bold transition-all ${
                  prediction === i
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
              >
                {i}
              </button>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-2 gap-3">
          <button
            onClick={() => placeDigitTrade('over', prediction)}
            disabled={!isConnected}
            className="flex items-center justify-center space-x-2 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg font-semibold transition-all disabled:opacity-50"
          >
            <ArrowUp className="w-4 h-4" />
            <span>Over {prediction}</span>
          </button>
          <button
            onClick={() => placeDigitTrade('under', prediction)}
            disabled={!isConnected}
            className="flex items-center justify-center space-x-2 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg font-semibold transition-all disabled:opacity-50"
          >
            <ArrowDown className="w-4 h-4" />
            <span>Under {prediction}</span>
          </button>
        </div>
      </div>
    </div>
  );

  const renderDiffMatchMode = () => (
    <div className="space-y-4">
      <div className="bg-gradient-to-r from-green-600/20 to-teal-600/20 rounded-lg p-4 border border-green-500/30">
        <div className="flex items-center space-x-2 mb-3">
          <Zap className="w-5 h-5 text-green-400" />
          <h3 className="font-semibold text-white">Diff | Match Mode</h3>
        </div>
        
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm text-gray-400 mb-1">Stake</label>
            <input
              type="number"
              value={settings.stake}
              onChange={(e) => handleSettingChange('stake', parseFloat(e.target.value))}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              min="0.1"
              step="0.1"
            />
          </div>
          <div>
            <label className="block text-sm text-gray-400 mb-1">Duration (ticks)</label>
            <input
              type="number"
              value={settings.duration}
              onChange={(e) => handleSettingChange('duration', parseInt(e.target.value))}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              min="1"
              max="10"
            />
          </div>
        </div>

        <div className="mb-4">
          <label className="block text-sm text-gray-400 mb-2">Prediction Digit</label>
          <div className="flex space-x-1">
            {Array.from({ length: 10 }, (_, i) => (
              <button
                key={i}
                onClick={() => setPrediction(i)}
                className={`w-8 h-8 rounded text-sm font-bold transition-all ${
                  prediction === i
                    ? 'bg-green-600 text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
              >
                {i}
              </button>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-2 gap-3">
          <button
            onClick={() => placeDigitTrade('diff', prediction)}
            disabled={!isConnected}
            className="flex items-center justify-center space-x-2 py-3 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg font-semibold transition-all disabled:opacity-50"
          >
            <Zap className="w-4 h-4" />
            <span>Diff {prediction}</span>
          </button>
          <button
            onClick={() => placeDigitTrade('match', prediction)}
            disabled={!isConnected}
            className="flex items-center justify-center space-x-2 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-semibold transition-all disabled:opacity-50"
          >
            <Target className="w-4 h-4" />
            <span>Match {prediction}</span>
          </button>
        </div>
      </div>
    </div>
  );

  const renderRiseFallMode = () => (
    <div className="space-y-4">
      <div className="bg-gradient-to-r from-orange-600/20 to-red-600/20 rounded-lg p-4 border border-orange-500/30">
        <div className="flex items-center space-x-2 mb-3">
          <TrendingUp className="w-5 h-5 text-orange-400" />
          <h3 className="font-semibold text-white">Rise | Fall Mode</h3>
        </div>

        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm text-gray-400 mb-1">Stake</label>
            <input
              type="number"
              value={settings.stake}
              onChange={(e) => handleSettingChange('stake', parseFloat(e.target.value))}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              min="0.1"
              step="0.1"
            />
          </div>
          <div>
            <label className="block text-sm text-gray-400 mb-1">Duration (ticks)</label>
            <input
              type="number"
              value={settings.duration}
              onChange={(e) => handleSettingChange('duration', parseInt(e.target.value))}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              min="1"
              max="10"
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-3">
          <button
            onClick={() => placeBinaryTrade('call')}
            disabled={!isConnected}
            className="flex items-center justify-center space-x-2 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg font-semibold transition-all disabled:opacity-50"
          >
            <TrendingUp className="w-4 h-4" />
            <span>Rise</span>
          </button>
          <button
            onClick={() => placeBinaryTrade('put')}
            disabled={!isConnected}
            className="flex items-center justify-center space-x-2 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg font-semibold transition-all disabled:opacity-50"
          >
            <TrendingDown className="w-4 h-4" />
            <span>Fall</span>
          </button>
        </div>
      </div>
    </div>
  );

  const renderQuadrantMode = () => {
    const handleQuadrantTrade = async (direction: TradeDirection, confidence: number) => {
      try {
        console.log('Trading Panel: Handling quadrant trade', { direction, confidence });

        // Convert quadrant direction to trade direction
        const tradeDirection: TradeDirection = direction === 'rise' ? 'up' : 'down';

        await executeTrade({
          mode: 'quadrant',
          direction: tradeDirection,
        });

        toast.success(`Quadrant signal: ${direction.toUpperCase()} (${(confidence * 100).toFixed(0)}% confidence)`);
      } catch (error) {
        console.error('Trading Panel: Quadrant trade execution failed', error);
        toast.error(`Quadrant trade failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    };

    return (
      <div className="space-y-4">
        <div className="bg-gradient-to-r from-violet-600/20 to-purple-600/20 rounded-lg p-4 border border-violet-500/30">
          <div className="flex items-center space-x-2 mb-3">
            <Grid3X3 className="w-5 h-5 text-violet-400" />
            <h3 className="font-semibold text-white">Quadrant Strategy Mode</h3>
          </div>
          <p className="text-sm text-gray-300 mb-4">
            Advanced digit sequence analysis using the Quadrant Formula for binary options trading.
          </p>

          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm text-gray-400 mb-1">Stake</label>
              <input
                type="number"
                value={settings.stake}
                onChange={(e) => handleSettingChange('stake', parseFloat(e.target.value))}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                min="0.1"
                step="0.1"
              />
            </div>
            <div>
              <label className="block text-sm text-gray-400 mb-1">Duration (ticks)</label>
              <input
                type="number"
                value={settings.duration}
                onChange={(e) => handleSettingChange('duration', parseInt(e.target.value))}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                min="1"
                max="10"
              />
            </div>
          </div>

          <QuadrantWidget
            aiService={getAIService()}
            onTradeSignal={handleQuadrantTrade}
            className="bg-gray-700/50 border border-gray-600"
          />
        </div>
      </div>
    );
  };

  const renderCurrentMode = () => {
    switch (currentMode) {
      case 'ai':
        return renderAIMode();
      case 'quadrant':
        return renderQuadrantMode();
      case 'over_under':
        return renderOverUnderMode();
      case 'diff_match':
        return renderDiffMatchMode();
      case 'rise_fall':
        return renderRiseFallMode();
      default:
        return null;
    }
  };

  return (
    <div className={`bg-gray-800 rounded-lg border border-gray-700 ${className}`}>
      <div className="p-4 border-b border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold text-white">Trading Panel</h2>
          <button
            onClick={() => setShowSettings(!showSettings)}
            className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-all"
          >
            <Settings className="w-5 h-5" />
          </button>
        </div>
        
        {renderModeSelector()}
      </div>

      <div className="p-4">
        {renderCurrentMode()}
      </div>

      {!isConnected && (
        <div className="p-4 border-t border-gray-700">
          <div className="bg-red-600/20 border border-red-500/30 rounded-lg p-3 text-center">
            <p className="text-red-400 text-sm">
              Not connected to trading server. Please check your connection.
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default TradingPanel;
